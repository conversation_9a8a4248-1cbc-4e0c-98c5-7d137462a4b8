\NeedsTeXFormat{LaTeX2e}
\ProvidesPackage{aas-style}[2024/06/19 v1.0 Custom AAS Style]

\newcommand{\song}{\CJKfamily{song}}
\newcommand{\fs}{\CJKfamily{fs}}
\newcommand{\kai}{\CJKfamily{kai}}
\newcommand{\hei}{\CJKfamily{hei}}
\newcommand{\li}{\CJKfamily{li}}
\newcommand{\you}{\CJKfamily{you}}
\let\songti\song
\let\fangsong\fs
\let\kaishu\kai
\let\heiti\hei
\let\lishu\li
\let\youyuan\you

\makeatletter
\def\ps@aasheadings{
    \let\@evenhead\@empty\let\@evenfoot\@empty
    \let\@oddhead\@empty\let\@oddfoot\@empty
    \def\@evenhead{%
        \vbox{%
            \vskip 3.2mm%
            \hbox to \textwidth {\hbox{\zihao{5-}\thepage} \hfill \hbox{\zihao{5-}\song 自 动 化 学 报} \hfill \hbox{\zihao{5-} XX\hskip 0.18em{\song 卷\ }}}%
            \vskip 2.0mm%
            \hbox to \textwidth{\noindent\rule[2mm]{\textwidth}{0.5pt}}%
        }%
    }
    \def\@oddhead{%
        \vbox{%
            \vskip 3.2mm%
            \hbox to \textwidth {\quad {\zihao{5-} X\hskip 0.18em}{\song 期}\hfill{\small\zihao{5-}\song Shang et al.: Preparation of Papers for Acta Automatica Sinica}\hfill {\zihao{5-}\thepage}}%
            \vskip 2.0mm%
            \hbox to \textwidth{\noindent\rule[2mm]{\textwidth}{0.5pt}}%  
        }
    }%
}
\makeatother

\endinput