\documentclass{ctextemp_aas}
\usepackage{xeCJK}
\setCJKmainfont{SimSun}
\setCJKsansfont{SimHei}
\setCJKmonofont{FangSong}


\usepackage{multicol}
\usepackage{ctextemp_psfig}
\usepackage{subfigure}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{amsthm}
\usepackage{amsfonts}
\usepackage{graphicx}
\usepackage{epstopdf}
\usepackage{url}
\usepackage{ccaption}
\usepackage{booktabs} % ߱
\usepackage{ctextemp_flushend}

\setcounter{page}{1}

\makeatletter
\def\ps@aasheadings{
    \let\@evenhead\@empty\let\@evenfoot\@empty
    \let\@oddhead\@empty\let\@oddfoot\@empty
    \def\@evenhead{%
        \vbox{%
            \vskip 2.2mm%
            \hbox to \textwidth {\small\thepage \quad X期 \hfill 商淑琳等:自动化学报论文撰写指南 \hfill \hbox{自}\kern1em\hbox{动}\kern1em\hbox{化}\kern1em\hbox{学}\kern1em\hbox{报}}%
            \vskip 1.5mm%
            \hbox to \textwidth{\noindent\rule[2mm]{\textwidth}{0.5pt}}%
        }%
    }
    \def\@oddhead{%
        \vbox{%
            \vskip 2.2mm%
            \hbox to \textwidth {\small X期 \hfill 商淑琳等:自动化学报论文撰写指南 \hfill \hbox{自}\kern1em\hbox{动}\kern1em\hbox{化}\kern1em\hbox{学}\kern1em\hbox{报} \quad \thepage}%
            \vskip 1.5mm%
            \hbox to \textwidth{\noindent\rule[2mm]{\textwidth}{0.5pt}}%
        }%
    }%
}
\makeatother

\begin{document}




\newtheoremstyle{aastheorem}%
  {2pt}{2pt}%
  {}{0pt}%
  {}{.}%
  {1em}%
  {\thmname{#1} \thmnumber{#2}\thmnote{ (#3)}}


\theoremstyle{aastheorem}

\newtheorem{theorem}{定理}
\newtheorem{example}{例}
\newtheorem{definition}{定义}
\newtheorem{corollary}{推论}
\newtheorem{lemma}{引理}


\cntitle{{\hei\qquad 自动化学报论文撰写指南}

\thanks{收稿日期: 
XXXX-XX-XX;
\quad
录用日期: 
XXXX-XX-XX}

\thanks{Manuscript received
Month Date, Year;
accepted
Month Date, Year}

\thanks{国家重点基础研究发展计划 (973 计划) (XXXXX), 国家高技术研究发展计划 (863 计划) (XXXXX), 国家自然科学基金 (XXXXX) 资助}

\thanks{Supported by National Basic Research Program of China (973 Program) (XXXXX),
National High Technology Research and Development Program of China (863 Program) (XXXXX), and
National Natural Science Foundation of China (XXXXX)}

\thanks{本文责任编委 XXX}

\thanks{Recommended by Associate Editor BIAN Wei}

\thanks{1.
中国科学院自动化研究所高技术创新中心, 北京 100190
\quad 2.
中国科学院自动化研究所模式识别国家重点实验室, 北京 100190
\quad 3.
中国科学院自动化研究所, 北京 100190
\quad 4. 中国科学院自动化研究所《自动化学报》(英文版)编辑部, 北京 100190
\quad 5. 中国科学院自动化研究所《自动化学报》编辑部, 北京 100190}

\thanks{1.
Hi-Tech Innovation Center, Institute of Automation, Chinese Academy of Sciences, Beijing
100190
\quad 2.
National Laboratory of Pattern Recognition,
Institute of Automation, Chinese Academy of Sciences, Beijing 100190
\quad 3.
Editorial
Institute of Automation, Chinese Academy of Sciences, Beijing 100190
\quad 4. Editorial
Office of {\sl IEEE/CAA Journal of Automatica Sinica (JAS)}, Institute of Automation,
Chinese Academy of Sciences, Beijing 100190
\quad 5. Editorial
Office of {\sl Acta Automatica Sinica}, Institute of Automation,
Chinese Academy of Sciences, Beijing 100190
}}

\cnauthor{商淑琳~$^{\scriptscriptstyle 1,\,2}$
\kern1em
左念明~$^{\scriptscriptstyle2}$
}

\cnabstract{摘要应是全文的缩影. 摘要内容包括研究目的、方法和结论, 注意不是背景介绍, 力求简明扼要, 不能出现公式和参考文献. 英文摘要须用心写, 遵循英文习惯, 语法和拼写无误, 用词准确.}

\cnkeyword{关键词~1, 关键词~2, 关键词~3, 关键词~4, 关键词~5}

\doi{10.16383/j.aas.cxxxxxx}

\entitle{Preparation of Papers for Acta Automatica Sinica}

\enauthor{SHANG Shu-Lin$^{1,\,2}$
\qquad
ZUO Nian-Ming$^2$
}

\enabstract{An abstract should be a concise summary of the
significant items in the paper, including the results and
conclusions. Define all nonstandard symbols,
abbreviations and acronyms used in the abstract. Do not cite
references in the abstract.}

\enkeyword{Keyword 1, keyword 2, keyword 3, keyword 4, keyword 5}

\cnaddress{商淑琳, 左念明. 自动化学报论文撰写指南. 自动化学报, 202X,
\textbf{XX}(X): X$-$X}

\enaddress{Shang Shu-Lin, Zuo Nian-Ming.
Preparation of papers for Acta Automatica Sinica.
\textsl{Acta Automatica Sinica}, 202X, \textbf{XX}(X): X$-$X}

\maketitle
\thispagestyle{aas@firstheadings}
\pagestyle{aasheadings}




%第一段为\textbf{引文格式},请勿删除.

本文是《自动化学报》的稿件 \LaTeX 模板一个简单的使用说明. 模板的运行支持中文环境 CCT. 第一段为\textbf{\textbf{引文格式}}, 请勿删除~$^{[1]}$.


\section{准备工作}

在工作的开始之前, 需要在 \verb|\cntitle| 命令中输入中文标题, 在 \verb|\cnauthor| 命令中输入中文作者, 在 \verb|\cnaddress| 命令中输入中文地址, 在 \verb|\email| 命令中输入 \verb|\email| 地址, 在 \verb|\cnabstract| 命令中输入中文摘要, 在 \verb|\cnkeyword| 命令中输入中文的关键词; 同理, 在 \verb|\entitle| 命令中输入英文标题, 在 \verb|\enauthor| 命令中输入英文作者, 在 \verb|\enaddress| 命令中输入英文地址, 在 \verb|\enabstract| 命令中输入英文摘要, 在 \verb|\enkeyword| 命令中输入英文的关键词. 最后, 在 \verb|\maketitle| 命令中生成这些内容.

论文首页应包括以下内容: 中英文的题目、作者姓名、作者详细工作单位和通讯地址及邮政编码 (基金资助放当页脚注位置)(请投稿时删除全部作者信息)、摘要和关键词 (4\,$\sim$\,10 个). 若稿件内容为原始性成果 (未在任何刊物上发表过), 请在论文第一页的脚注注明. 基金资助项目请在首页用中英文脚注说明, 基金项目英文名称须写全称而非缩写.

\subsection{文章~{\bf section}~标题的写法}

文章 \verb|section/subsection/subsubsection| 命令中的标题, 中文使用黑体; 如果要出现英文的时候, 英文使用粗体. 要生成当前~subsection 标题, 请按照如下格式书写
\begin{verbatim}
\subsection{文章~{\bf section}~标题的写法}
\end{verbatim}

\subsection{模板支持几种中英文的字体}

如果您的 \LaTeX 系统已经安装了 \LaTeX 标准中文字体库, 本模板支持~6 种中文字体, 分别为宋体 (song), 仿宋体 (fs), 楷体 (kai), 黑体 (hei), 隶书 (li) 和幼圆 (you), 默认值为宋体和黑体, 详细内容可参考~aas.cls 文件的~64\,$\sim$\,69 行. 如果您的.tex 文件编译时出现如下字体缺失的提示时:
\begin{verbatim}
LaTeX Font Warning: Some font shapes were
not available, defaults substituted.
\end{verbatim}
请参考建议: 1) 检查您的 \LaTeX 系统是否已经安装了所使用的字体; 2) 检查您的字体名称是否正确, 比如仿宋体的名称为~fangsong, 而不是~fs, 这时需要相应地修改~aas.cls 文件的~64\,$\sim$\,69 行.

\subsection{{\bf pdf}~文件的生成方式}

直接对.tex 文件进行编译, 可自动生成~pdf 文件,
或者使用~dvi2pdf 命令对~dvi 文件生成~pdf 文件. 注意,
一般不要使用其他方式生成~pdf 文件.

\section{录用稿件格式的几点要求}

务求论点明确, 论证严谨, 文字通顺, 文字简练, 标点正确. 录用稿件请按照录用通知的要求对文章进行修改润色.
% 综述性文章的篇幅原则上不超过7页; 论文不超过4页; 评论性的文章可以放宽. 凡属国家自然科学基金、省部级以上重点攻关及“863”高科技项目的稿件将优先发表.

\subsection{名词、术语和单位的书写}

全篇同一名词、术语和符号须前后一致. 每个符号, 在它第一次出现时必须予以说明, 习惯用法除外. 物理量单位采用国家标准单位 (SI), 用规范化符号表示, 如压强单位应为~P (帕斯卡), 不用~``毫米汞柱''. 外文缩写词在第一次出现时注明全称. 文章统一使用英文的标点、符号, 英文的标点和数字后加一个空格, 中文的标点和数字后面不需加空格.


\subsection{定理和证明}

\begin{theorem}
这是一个定理
\end{theorem}

\begin{example}
这是一个例子
\end{example}

\begin{definition}
这是一个定义
\end{definition}

\begin{corollary}
这是一个推论
\end{corollary}

\begin{lemma}
这是一个引理
\end{lemma}


\subsection{数学公式}



公式中的变量和常量须全篇统一编码, 按照国家标准规定使用符号;
普通变量用斜体; 矢量和矩阵用粗/斜体字母;
5 个常用数集~$\mathbf{R}$、$\mathbf{C}$、$\mathbf{N}$、$\mathbf{Z}$、$\mathbf{Q}$ 用粗正体,
其它集用斜体; 上下标的位置和书写须准确 (上下文须注意全篇统一);
专用函数如~exp、sin、cos、lim、log、det 等用正体; 微分、导数、偏微分符号、数学常数用正体.
大写希腊字母~$\Gamma\,\Delta\,\Theta\,\Lambda\,\Xi\,\Pi\,\Sigma\,
\Upsilon\,\Phi\,\Psi\,\Omega$ 用正体, 为变量 (常量) 时用大写斜体. et al.,
etc., e.g., s.t. 用正体.

式~(1)和(2) 是两个简单的数学公式的例子.
\begin{equation}
Y_t^{e,k} = r_{}^e\left( {{{\boldsymbol s}_t}} \right) + \gamma \mathop {\max }\limits_{{a^k}} \left( {Q\left( {{{\boldsymbol s}_{t + 1}},{a^k};{{\boldsymbol \theta} ^k}} \right)} \right)
\end{equation}
\begin{equation}
L\left( {{{\boldsymbol \theta} ^k}} \right) = {P_s}{L^o}\left( {{{\boldsymbol \theta} ^k}} \right) + \left( {1 - {P_s}} \right){L^e}\left( {{{\boldsymbol \theta} ^k}} \right)
\end{equation}

式~(3) 是一个通栏的公式的例子. 在这个例子中使用了~\verb|\end{multicols}|和 \verb|\begin{multicols}{2}| 以及~\verb|\onecolumn| 命令.

\subsection{图表}


文中图表只附最必要的, 并附中英文的图题和表题.
图和表的分辨率用国家标准中推荐使用的线性尺寸,
图和表的绘制应遵循一个重要原则~``文字不如表/图'' 的原则.  1) 字体:
图表中注字用英文~Times New Roman 字体, 变量用斜体,
其它内容的字体为平台. 2) 请用三线表格式. 3) 存储格式: eps
\linebreak 存储选项为: 预览图, 编码~ASCII, 不选~Postscript.
请将这些图片的原始图~(.fig, .vsd, .doc 等)
放在一个单独的文件夹内压缩打包~(.zip或.rar), E-mail 给学报编辑部.
下面是图表的几种生成方式. 图~1 是一个双栏图片示例,
图~2 是一个通栏图片示例,
在~\verb|\end{multicols}|和\verb|\begin{multicols}{2}| 以及~\verb|\onecolumn| 命令.

\begin{center}
{\centering
\vbox{\centerline{\includegraphics[width=6cm]{Fig1.eps}} \vskip1mm {\small
图\ 1\quad 双栏图片示例
\\
Fig. 1\quad  An  example graph in two column }}}
\end{center}

《自动化学报》的表格采用的是三线表格式. 表~1 是一个双栏的表格示例, 表~2 是一个通栏的表格示例, 在~\verb|\end{multicols}|和\verb|\begin{multicols}{2}| 以及~\verb|\onecolumn| 命令.

\onecolumn
\begin{equation}
\begin{array}{l}
{L^e}\left( {{{\boldsymbol \theta} ^k}} \right)= {\mathrm{E}_{{\boldsymbol s}\sim\psi ,{\boldsymbol a}\sim\varphi ',\varphi '\sim{\pi ^*}\left( \psi  \right)}}\left[ {\sum\limits_{k = 1}^N {{{\left( {Y_{}^{e,k} - {Q^k}\left( {{\boldsymbol s},a_{}^k;{{\boldsymbol \theta} ^k}} \right)} \right)}^2}} } \right]
\end{array}
\end{equation}

\begin{center}
{\centering
\vbox{\centerline{\includegraphics[width=16cm]{Fig2.eps}} \vskip1mm {\small
图\ 2\quad 通栏图片示例
\\
Fig. 2\quad  An example graph in one column }}}
\end{center}

\begin{center}
\vbox{\centering{\small 表~2\quad 通栏表格示例
\\
Table 2 \quad An example table in one column } \vskip2mm
\renewcommand{\baselinestretch}{1.2}
{\footnotesize\centerline{\tabcolsep=15pt\begin{tabular*}{\textwidth}{ccccc}
\toprule
对手 (OPP)                   & 比分 (CSU : OPP) & 我队禁区控球时间 & 对手禁区控球时间 & 我队禁区控球时间比值 \\
\hline
Cyberoos2001        & \phantom{0}3:0  & 71.5 & 28.5 & 2.51 \\
FCPortugal2001      & \phantom{0}1:0  & 68.4 & 31.6 & 2.16 \\
Gemini              & 26:0 & 59.7 & 40.3 & 1.48 \\
Harmony             & \phantom{0}3:0  & 69.9 & 30.1 & 2.32 \\
Lazarus             & 11:0 & 57.3 & 42.7 & 1.34 \\
MRB                 & \phantom{0}2:0  & 63.2 & 32.8 & 1.93 \\
SBCe                & \phantom{0}4:1  & 65.8 & 34.2 & 1.92 \\
UvA\_Trilearn\_2001 & \phantom{0}1:0  & 54.9 & 44.1 & 1.24 \\
UTUtd               & 10:0 & 70.7 & 29.3 & 2.41 \\
WrightEagle2001     & \phantom{0}3:1  & 66.2 & 33.8 & 1.96 \\
Average             &      & 64.8 & 35.2 & 1.84 \\
\bottomrule
\end{tabular*}}}}\end{center}
\begin{multicols}{2}%开始双栏显示

\begin{center}
\vbox{\centering{\small 表\ 1 \quad 双栏表格示例
\\
Table 1 \quad An example table in two column} \vskip2mm
\renewcommand{\baselinestretch}{1.2}
{\footnotesize\centerline{\tabcolsep=15pt\begin{tabular}{ccccc}
\toprule
基因 & BCDQN & PMDQN\\
\hline
1 & TP53 & TP53\\
2 & FAM91A1 & PIK3CA\\
3 & TNFRSF11B &TG\\
4 & KCNQ3 & HHLA1\\
5 & MYC & ASAP1\\
6 & COL14A1 &CASC8\\
7 & CCDC26 & SNORA12\\
8 & CCN3 & MYC\\
9 & PVT1 & PVT1\\
10 & DSCC1 & RN7SL329\\
\bottomrule
\end{tabular}}}}\end{center}

\subsection{参考文献}

参考文献只列公开出版的文献. 内部资料、未发表或待发表的文献 (学位论文除外) 等请勿作为参考文献. 如必须引用时, 可放在当页的脚注 (书写格式与参考文献相同). 参考文献要按文中出现的先后次序编号~$1,2, \cdots$, 并顺序排列. 文中引用参考文献时须加注, 用~[] 表示. 参考文献中不使用任何连接词. 英文作者姓名书写时姓在前名在后, 可用~``et al.'', 但必须标注全部作者姓名. 注意参考文献的各项内容应齐全, 否则将影响文章的正常出版和被证引用, 未引用的文献不要列在参考文献列表中. 格式可参照模板末尾示例.

\TeX 的参考文献的引用比较方便, 比如要引用~Knuth 的~The \TeX book, 只需要输入~``The \TeX book\verb|$^{[1]}$|'' 得到想要的效果~``The \TeX book$^{[1]}$''. 对中文参考文献~``多模态优化算法\,\!$^{[2]}$'' 得到想要的效果~``多模态优化算法\,\!$^{[2]}$''.

\subsection{作者简介和照片}

文章末页给出所有作者的中英文简介, 篇幅不要超过~100 字, 内容包括出生年月、性别、职称、学习工作经历, 以及研究方向.


作者照片请选用简单背景的证件照片.

% 如果作者照片没有, 请用biographynophoto命令只写作者简介. 如果所有作者都没有照片, 这时没有照片文件可以引用.

\section{其他注意事项}

1) 对本刊的稿件实行三审制, 稿件录用后请勿再投其它刊物.
% 修改加工后的稿件请按录用通知要求在指定日期内返回编辑部. 如不能按期返回, 录用通知将自动失效, 稿件按自动撤稿处理.

2) 请作者仔细按照录用通知和稿件模板中的各项要求认真加工修改稿件, 若修改稿不符合要求, 编辑部有权将稿件退回作者修改直至符合要求为止,  否则造成的稿件不能及时刊出将由作者本人负责.
% 作者投稿时请确定, 文章默认的第一作者为通讯作者. 如果第一作者发生变动或通讯作者的联系方式有变动, 请及时通知编辑部.

3) 文章在编辑加工过程中, 编辑部会将~pdf 校样通过~E-mail 发给全部作者, 请各位作者对自己的稿件内容进行校对. 对编辑部发出的校样请在两天内返回. 校样中内容的修改请慎重, 需要和同事或专家对自己的文章进行一次全面的检查, 以免出现新的错误和疏漏, 影响文章的顺利出版.

4) 对修改稿加工过程中若有疑问请及时与编辑部联系, 谢谢合作!

\section{结论}

文中的结论部分不要和摘要和前言的内容重复.

\section*{致谢}

论文作者可以在此处向支持和帮助本研究和论文写作的组织或个人表示感谢.
注意, 资助本研究的基金项目已直接标注在首页页脚处, 不必在此处再次感谢.

\begin{thebibliography}{99}
\zihao{6} \addtolength{\itemsep}{0.2em} \urlstyle{rm}
\bibitem{1} Wang X S, Gu Y , Cheng Y H, Liu A P, Chen C L P. Approximate policy-based accelerated deep reinforcement learning. {\sl IEEE Transactions on Neural Networks and Learning Systems}, 2020, {\bf 31}(6): 1820$-$1830

\bibitem{2} Zhang C Q, Han Z B, Cui Y J, Hu Q. CPM-Nets: Cross partial multi-view networks. In: Proceedings of Advances in Neural Information Processing Systems (NIPS). Vancouver, BC, Canada: Curran Associates, 2019. 4077$-$4087

 \bibitem{3} Liu Jian, Gu Yang, Cheng Yu-Hu, Wang Xue-Song. Prediction of breast cancer pathogenic genes based on multi-agent reinforcement learning. {\sl Acta Automatica Sinica}, DOI: 10.16383/j.aas.c210583 \\
 (刘健, 谷洋, 程玉虎, 王雪松. 基于多智能体强化学习的乳腺癌致病基因预测. 自动化学报, DOI: 10.16383/j.aas.c210583)

 \bibitem{4} Sun Y, Wang X G, Tang X O. Deep learning face representation from predicting 10 000 classes. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR). Columbus, OH, USA: IEEE, 2014. 1891$-$1898

\bibitem{5} Jin K H, McCann M T, Froustey E, Unser M. Deep convolutional neural network for inverse problems in imaging. {\sl IEEE Transactions on Image Processing}, 2017, {\bf 26}(9): 4509$-$4522

% \bibitem{6} Pang W Y, Fan J L, Jiang Y, LewisFrank L. Optimal output regulation of partially linear discrete-time systems using reinforcement learning. {\sl Acta Automatica Sinica}, 2021, {\bf 47}(x): 1$-$12 doi: 10.16383/j.aas.c190853, to be published \\
% (庞文宇, 范家璐, 蒋瑛, LewisFrank L. 基于强化学习的部分线性离散时间系统最优输出调节. 自动化学报, 2021, {\bf 47}(x): 1$-$12 doi: 10.16383/j.aas.c190853)

% \bibitem{7} Shi W, Feng Y H, Cheng G Q, Huang H L, Huang J C, Liu Z, et al. Research on multi-aircraft cooperative air combat method based on deep reinforcement learning. {\sl Acta Automatica Sinica}, 2021, {\bf 47}(x): 1$-$14 doi: 10.16383/j.aas.c201059, to be published \\
% (施伟, 冯宇航, 程光权, 黄海龙, 黄建成, 刘准, 等. 基于深度强化学习的多机协同空战方法研究. 自动化学报, 2021, {\bf 47}(x): 1$-$14 doi: 10.16383/j.aas.c201059)

\bibitem{8} Cheng Y H, Huang L Y, Wang X S. Authentic boundary proximal policy optimization. {\sl IEEE Transactions on Cybernetics}, DOI: 10.1109/TCYB.2021.3051456

\bibitem{9} Zhou B, Lapedriza A, Khosla A, Khosla A, Oliva A, Torralba A. Places: A 10 million image database for scene recognition. {\sl IEEE Transactions on Pattern Analysis and Machine Intelligence}, 2018, {\bf 40}(6): 1452$-$1464
\end{thebibliography}

\begin{biography}[ssl.eps]
\noindent{\hei
商淑琳
}\quad
中国科学院自动化研究所博士研究生.
2002 年获北京师范大学信息学院电子系学士学位.
主要研究方向为图像和视频压缩编码.\\E-mail: aas\<EMAIL>

\noindent({\bf
SHANG Shu-Lin
}\quad
Ph.D. candidate at the
Institute of Automation, Chinese Academy of Sciences. He received
his bachelor degree from Beijing Normal University in 2002. His research
interest covers image compression and video coding.)
\end{biography}

\begin{biography}[nmzuo.eps]
\noindent{\hei
左念明
}\quad
中国科学院自动化研究所博士研究生.
2002 年获山东大学数学学院学士学位.
主要研究方向为医学图像, CT 图像重建. 本文通信作者.
\\E-mail: aas\<EMAIL>

\noindent({\bf
ZUO Nian-Ming
}\quad
Ph.D. candidate at the Institute of Automation, Chinese Academy of Sciences. He received his bachelor degree from Shandong University in 2002. His research interest covers medical CT image reconstruction and medical image processing. Corresponding author of this paper.)
\\
\\
\\
\\
\\
\\
\\
\\
\\
\\
\\
\\
\end{biography}

\end{multicols}
\end{document}
