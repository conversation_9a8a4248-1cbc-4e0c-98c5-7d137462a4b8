#!/usr/bin/env python3
"""
测试批处理推理功能
"""

import torch
import numpy as np
import time
from pathlib import Path
from global_inference_system import GlobalInferenceSystem, GlobalInferenceConfig

def test_batch_inference():
    """测试批处理推理"""
    print("🧪 测试批处理推理功能")
    print("=" * 50)
    
    try:
        # 创建配置
        config = GlobalInferenceConfig()
        config.batch_size = 96
        config.splits = ["train"]  # 只测试train分割
        
        print(f"📊 批处理大小: {config.batch_size}")
        print(f"📂 测试分割: {config.splits}")
        
        # 创建推理系统
        print("🔧 初始化推理系统...")
        inference_system = GlobalInferenceSystem(config)
        
        print("✅ 推理系统初始化完成")
        print(f"📦 模型已加载: {config.checkpoint_path}")
        print(f"🎯 目标性能: {config.target_fps} FPS")
        
        # 获取少量测试图片
        print("\n📂 获取测试图片...")
        image_paths = inference_system._get_image_paths("train")
        
        if not image_paths:
            print("❌ 未找到测试图片")
            return
        
        # 限制测试图片数量
        test_count = min(config.batch_size * 3, len(image_paths))  # 测试3个批次
        test_paths = image_paths[:test_count]
        
        print(f"📊 测试图片数量: {len(test_paths)}")
        print(f"📦 批次数量: {len(test_paths) // config.batch_size + 1}")
        
        # 测试批处理
        print("\n🚀 开始批处理测试...")
        
        total_start_time = time.time()
        processed_count = 0
        
        for batch_start in range(0, len(test_paths), config.batch_size):
            batch_end = min(batch_start + config.batch_size, len(test_paths))
            batch_paths = test_paths[batch_start:batch_end]
            
            print(f"\n📦 批次 {batch_start//config.batch_size + 1}: 处理 {len(batch_paths)} 张图片")
            
            # 预处理批次图像
            batch_start_time = time.time()
            batch_tensors = []
            valid_paths = []
            
            for image_path in batch_paths:
                image_tensor = inference_system._preprocess_image(image_path)
                if image_tensor is not None:
                    batch_tensors.append(image_tensor)
                    valid_paths.append(image_path)
            
            preprocess_time = time.time() - batch_start_time
            print(f"   ⏱️ 预处理时间: {preprocess_time:.3f}s ({len(batch_tensors)} 张有效)")
            
            if not batch_tensors:
                print("   ⚠️ 批次中无有效图片")
                continue
            
            # 批量推理
            inference_start_time = time.time()
            try:
                batch_results = inference_system._inference_batch(batch_tensors, valid_paths)
                inference_time = time.time() - inference_start_time
                
                # 统计结果
                valid_results = [r for r in batch_results if r is not None]
                total_detections = sum(r['performance']['num_detections'] for r in valid_results)
                avg_fps = np.mean([r['performance']['fps'] for r in valid_results]) if valid_results else 0
                avg_latency = np.mean([r['performance']['latency_ms'] for r in valid_results]) if valid_results else 0
                
                print(f"   ✅ 推理完成: {inference_time:.3f}s")
                print(f"   📊 有效结果: {len(valid_results)}/{len(batch_tensors)}")
                print(f"   🎯 总检测数: {total_detections}")
                print(f"   🚀 平均FPS: {avg_fps:.1f}")
                print(f"   ⏱️ 平均延迟: {avg_latency:.1f}ms")
                
                processed_count += len(valid_results)
                
            except Exception as e:
                print(f"   ❌ 批量推理失败: {e}")
                continue
        
        total_time = time.time() - total_start_time
        overall_fps = processed_count / total_time if total_time > 0 else 0
        
        print(f"\n🎉 批处理测试完成!")
        print(f"📊 总处理图片: {processed_count}")
        print(f"⏰ 总耗时: {total_time:.1f}s")
        print(f"🚀 整体FPS: {overall_fps:.1f}")
        print(f"🎯 目标达成: {'✅' if overall_fps >= config.target_fps else '❌'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_batch_inference()
