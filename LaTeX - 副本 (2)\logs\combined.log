2025-06-18T08:44:58.744Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-18T08:44:58.746Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-18T08:44:58.745Z [MCPServer] INFO: Parsed options: {}
2025-06-18T08:44:58.745Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-18T08:44:58.745Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-18T08:44:58.745Z [MCPServer] INFO: Current options: {}
2025-06-18T08:44:58.745Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-18T08:44:58.748Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-18T08:44:58.748Z [MCPServer] INFO: Starting relay server...
2025-06-18T08:44:58.748Z [RelayServerManager] INFO: Starting relay server process
2025-06-18T08:44:59.398Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:44:59.400Z [config] INFO: Loaded configuration from environment variables

2025-06-18T08:44:59.398Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:44:59.401Z [config] INFO: Configuration initialized

2025-06-18T08:44:59.406Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:44:59.409Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-18T08:44:59.406Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:44:59.409Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-18T08:44:59.880Z [RelayServerManager] INFO: Relay server started successfully
2025-06-18T08:44:59.881Z [MCPServer] INFO: Relay server started successfully
2025-06-18T08:44:59.881Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-18T08:44:59.881Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-18T08:44:59.881Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-18T08:44:59.908Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-18T08:44:59.908Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-18T08:44:59.908Z [WebSocketClient] WARN: Connection attempt 1 failed: 
2025-06-18T08:44:59.908Z [WebSocketClient] INFO: Retrying in 2 seconds...
2025-06-18T08:45:01.916Z [WebSocketClient] INFO: Connection attempt 2 of 3
2025-06-18T08:45:01.918Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-18T08:45:01.918Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-18T08:45:01.918Z [WebSocketClient] WARN: Connection attempt 2 failed: 
2025-06-18T08:45:01.918Z [WebSocketClient] INFO: Retrying in 2 seconds...
2025-06-18T08:45:02.934Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.938Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-18T08:45:02.935Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.938Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-18T08:45:02.935Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.938Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-18T08:45:02.935Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.938Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-18T08:45:02.936Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.939Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-18T08:45:02.936Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.939Z [OscClient] INFO: OSC-MSG-mc1pi6hnrsc: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-18T08:45:02.937Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.941Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-18T08:45:02.952Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.955Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-18T08:45:02.952Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.955Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-18T08:45:02.953Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:02.957Z [OscClient] INFO: OSC-MSG-mc1pi6hnrsc: Successfully sent

2025-06-18T08:44:59.400Z [config] INFO: Loaded configuration from environment variables
2025-06-18T08:44:59.409Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-18T08:45:02.938Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-18T08:45:02.938Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-18T08:44:59.401Z [config] INFO: Configuration initialized
2025-06-18T08:44:59.409Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-18T08:45:02.938Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-18T08:45:02.938Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-18T08:45:02.939Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-18T08:45:02.939Z [OscClient] INFO: OSC-MSG-mc1pi6hnrsc: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-18T08:45:02.941Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-18T08:45:02.957Z [OscClient] INFO: OSC-MSG-mc1pi6hnrsc: Successfully sent
2025-06-18T08:45:02.955Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-18T08:45:02.955Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-18T08:45:03.922Z [WebSocketClient] INFO: Connection attempt 3 of 3
2025-06-18T08:45:03.931Z [WebSocketServer] INFO: New WebSocket connection
2025-06-18T08:45:03.929Z [WebSocketClient] INFO: Connected to WebSocket server
2025-06-18T08:45:03.931Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:45:03.931Z [WebSocketServer] INFO: New WebSocket connection

2025-06-18T08:45:03.929Z [WebSocketClient] INFO: Testing connection (attempt 1 of 3)
2025-06-18T08:45:03.932Z [WebSocketClient] INFO: Connection test successful
2025-06-18T08:45:03.932Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-18T08:45:03.932Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-18T08:50:53.055Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-18T08:50:53.057Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-18T08:50:53.056Z [MCPServer] INFO: Parsed options: {}
2025-06-18T08:50:53.056Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-18T08:50:53.057Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-18T08:50:53.057Z [MCPServer] INFO: Current options: {}
2025-06-18T08:50:53.057Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-18T08:50:53.059Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-18T08:50:53.060Z [MCPServer] INFO: Starting relay server...
2025-06-18T08:50:53.060Z [RelayServerManager] INFO: Starting relay server process
2025-06-18T08:50:54.252Z [RelayServerManager] INFO: Relay server started successfully
2025-06-18T08:50:54.253Z [MCPServer] INFO: Relay server started successfully
2025-06-18T08:50:54.254Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-18T08:50:54.253Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-18T08:50:54.254Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-18T08:51:02.758Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.762Z [config] INFO: Loaded configuration from environment variables

2025-06-18T08:51:02.759Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.764Z [config] INFO: Configuration initialized

2025-06-18T08:51:02.760Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-18T08:51:02.769Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.774Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-18T08:51:02.760Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-18T08:51:02.761Z [WebSocketClient] WARN: Connection attempt 1 failed: 
2025-06-18T08:51:02.761Z [WebSocketClient] INFO: Retrying in 2 seconds...
2025-06-18T08:51:02.769Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.775Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-18T08:51:02.836Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.841Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-18T08:51:02.836Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.841Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-18T08:51:02.836Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.841Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-18T08:51:02.836Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.842Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-18T08:51:02.836Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.842Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-18T08:51:02.837Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.842Z [OscClient] INFO: OSC-MSG-mc1ppw6yjpc: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-18T08:51:02.839Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.844Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-18T08:51:02.842Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.847Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-18T08:51:02.842Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.847Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-18T08:51:02.844Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:02.849Z [OscClient] INFO: OSC-MSG-mc1ppw6yjpc: Successfully sent

2025-06-18T08:51:02.762Z [config] INFO: Loaded configuration from environment variables
2025-06-18T08:51:02.774Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-18T08:51:02.842Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-18T08:51:02.764Z [config] INFO: Configuration initialized
2025-06-18T08:51:02.775Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-18T08:51:02.841Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-18T08:51:02.841Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-18T08:51:02.842Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-18T08:51:02.842Z [OscClient] INFO: OSC-MSG-mc1ppw6yjpc: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-18T08:51:02.844Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-18T08:51:02.849Z [OscClient] INFO: OSC-MSG-mc1ppw6yjpc: Successfully sent
2025-06-18T08:51:02.841Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-18T08:51:02.847Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-18T08:51:02.847Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-18T08:51:04.770Z [WebSocketClient] INFO: Connection attempt 2 of 3
2025-06-18T08:51:04.779Z [WebSocketServer] INFO: New WebSocket connection
2025-06-18T08:51:04.774Z [WebSocketClient] INFO: Connected to WebSocket server
2025-06-18T08:51:04.776Z [RelayServerManager] ERROR: [RelayServer] 2025-06-18T08:51:04.779Z [WebSocketServer] INFO: New WebSocket connection

2025-06-18T08:51:04.774Z [WebSocketClient] INFO: Testing connection (attempt 1 of 3)
2025-06-18T08:51:04.776Z [WebSocketClient] INFO: Connection test successful
2025-06-18T08:51:04.776Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-18T08:51:04.777Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-21T15:45:02.999Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-21T15:45:03.002Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-21T15:45:03.000Z [MCPServer] INFO: Parsed options: {}
2025-06-21T15:45:03.000Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-21T15:45:03.001Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-21T15:45:03.001Z [MCPServer] INFO: Current options: {}
2025-06-21T15:45:03.002Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-21T15:45:03.004Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-21T15:45:03.004Z [MCPServer] INFO: Starting relay server...
2025-06-21T15:45:03.005Z [RelayServerManager] INFO: Starting relay server process
2025-06-21T15:45:03.421Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:03.422Z [config] INFO: Loaded configuration from environment variables

2025-06-21T15:45:03.422Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:03.423Z [config] INFO: Configuration initialized

2025-06-21T15:45:03.429Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:03.430Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-21T15:45:03.429Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:03.430Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-21T15:45:04.198Z [RelayServerManager] INFO: Relay server started successfully
2025-06-21T15:45:04.198Z [MCPServer] INFO: Relay server started successfully
2025-06-21T15:45:04.198Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-21T15:45:04.198Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-21T15:45:04.198Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-21T15:45:04.212Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-21T15:45:04.212Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-21T15:45:04.212Z [WebSocketClient] WARN: Connection attempt 1 failed: 
2025-06-21T15:45:04.212Z [WebSocketClient] INFO: Retrying in 2 seconds...
2025-06-21T15:45:04.590Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.591Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-21T15:45:04.590Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.592Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-21T15:45:04.590Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.592Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-21T15:45:04.591Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.592Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-21T15:45:04.591Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.592Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-21T15:45:04.591Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.592Z [OscClient] INFO: OSC-MSG-mc6etw7khdf: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-21T15:45:04.593Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.594Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-21T15:45:04.595Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.597Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-21T15:45:04.596Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.597Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-21T15:45:04.597Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:04.598Z [OscClient] INFO: OSC-MSG-mc6etw7khdf: Successfully sent

2025-06-21T15:45:03.422Z [config] INFO: Loaded configuration from environment variables
2025-06-21T15:45:03.430Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-21T15:45:04.592Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-21T15:45:04.592Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-21T15:45:03.423Z [config] INFO: Configuration initialized
2025-06-21T15:45:03.430Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-21T15:45:04.591Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-21T15:45:04.592Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-21T15:45:04.592Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-21T15:45:04.592Z [OscClient] INFO: OSC-MSG-mc6etw7khdf: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-21T15:45:04.594Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-21T15:45:04.598Z [OscClient] INFO: OSC-MSG-mc6etw7khdf: Successfully sent
2025-06-21T15:45:04.597Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-21T15:45:04.597Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-21T15:45:06.220Z [WebSocketClient] INFO: Connection attempt 2 of 3
2025-06-21T15:45:06.229Z [WebSocketServer] INFO: New WebSocket connection
2025-06-21T15:45:06.230Z [WebSocketClient] INFO: Connected to WebSocket server
2025-06-21T15:45:06.231Z [RelayServerManager] ERROR: [RelayServer] 2025-06-21T15:45:06.229Z [WebSocketServer] INFO: New WebSocket connection

2025-06-21T15:45:06.230Z [WebSocketClient] INFO: Testing connection (attempt 1 of 3)
2025-06-21T15:45:06.232Z [WebSocketClient] INFO: Connection test successful
2025-06-21T15:45:06.232Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-21T15:45:06.232Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
