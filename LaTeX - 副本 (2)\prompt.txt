参考已有htm文件论文格式模板，新建一个htm文件写一篇AI操作系统与AI Agent的论文，先在各大专业AI网站与Google学术研究获取先验知识，可以使用记忆与思考MCP，然后根据真实的参考论文进行学术论文写作，可以分派子任务但模式保持online-thinking不变

参考已有tex文件论文格式模板，新建一个tex文件写一篇AI操作系统与AI Agent的论文，先在各大专业AI网站与Google学术研究获取充分的先验知识，可以使用记忆与思考MCP，然后根据真实的参考论文进行学术论文写作，字数不少于3000字，具有一定的深刻的洞见

使用xelatex -interaction=nonstopmode编译

将单位恢复为模板中的作者所在单位重庆交通大学，1 引言放到下一页，2.1 操作系统发展回顾后面不要留这么大的空白，去除“7 参考文献“这个重复的标题，去除AIOS论文中多余的*号

用xelatex -interaction=nonstopmode template.tex直到成功编译，格式与模板pdf完全一致
xelatex -interaction=nonstopmode ctextemp_template.tex

参考CjC_template_fixed.tex能以utf-8编码正常显示中文,让ctextemp_template.tex也能正常显示中文，并成功用xelatex -interaction=nonstopmode template.tex编译，记录下转换的经验文档

联网搜索自动化学报论文模板tex文件在vscode终端直接编译成功的经验并试验
