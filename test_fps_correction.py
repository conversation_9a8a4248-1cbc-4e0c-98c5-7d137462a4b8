#!/usr/bin/env python3
"""
测试FPS修正计算
验证批处理FPS除以批大小的正确性
"""

import json
from pathlib import Path

def test_fps_correction():
    """测试FPS修正"""
    
    # 原始推理数据
    original_data = {
        'train': {'fps': 70882.0, 'images': 70000},
        'val': {'fps': 45111.2, 'images': 10000}, 
        'test': {'fps': 12661.5, 'images': 20000}
    }
    
    batch_size = 96
    
    print("🔧 FPS修正计算测试")
    print("=" * 50)
    
    corrected_data = {}
    total_corrected_fps = 0
    total_images = 0
    
    for split, data in original_data.items():
        # 修正FPS = 原始批处理FPS / 批大小
        corrected_fps = data['fps'] / batch_size
        corrected_data[split] = {
            'original_fps': data['fps'],
            'corrected_fps': corrected_fps,
            'images': data['images']
        }
        
        total_corrected_fps += corrected_fps * data['images']
        total_images += data['images']
        
        print(f"{split.upper()}:")
        print(f"  原始FPS: {data['fps']:,.1f}")
        print(f"  修正FPS: {corrected_fps:.1f}")
        print(f"  修正比例: {corrected_fps/data['fps']*100:.1f}%")
        print()
    
    # 计算加权平均FPS
    avg_corrected_fps = total_corrected_fps / total_images
    
    print("总体统计:")
    print(f"  总图片数: {total_images:,}")
    print(f"  平均修正FPS: {avg_corrected_fps:.1f}")
    print(f"  批大小: {batch_size}")
    
    # 与目标FPS对比
    target_fps = 30.0
    fps_ratio = avg_corrected_fps / target_fps
    
    print(f"\n性能评估:")
    print(f"  目标FPS: {target_fps}")
    print(f"  实际FPS: {avg_corrected_fps:.1f}")
    print(f"  性能比率: {fps_ratio:.1f}x")
    print(f"  是否达标: {'✅' if avg_corrected_fps >= target_fps else '❌'}")
    
    # 保存修正数据
    correction_summary = {
        'batch_size': batch_size,
        'correction_factor': 1/batch_size,
        'splits': corrected_data,
        'overall': {
            'total_images': total_images,
            'average_corrected_fps': avg_corrected_fps,
            'target_fps': target_fps,
            'performance_ratio': fps_ratio,
            'meets_target': avg_corrected_fps >= target_fps
        }
    }
    
    with open('fps_correction_summary.json', 'w', encoding='utf-8') as f:
        json.dump(correction_summary, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ FPS修正摘要已保存: fps_correction_summary.json")
    
    return correction_summary

if __name__ == "__main__":
    test_fps_correction()
