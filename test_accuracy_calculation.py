#!/usr/bin/env python3
"""
测试准确率计算功能
"""

import torch
import numpy as np
from pathlib import Path
from global_inference_system import GlobalInferenceSystem, GlobalInferenceConfig

def test_accuracy_calculation():
    """测试准确率计算功能"""
    print("🧪 测试准确率计算功能")
    print("=" * 50)
    
    try:
        # 创建配置
        config = GlobalInferenceConfig()
        config.debug_mode = True
        
        # 创建推理系统
        print("🔧 初始化推理系统...")
        inference_system = GlobalInferenceSystem(config)
        
        print("✅ 推理系统初始化完成")
        
        # 测试检测解析
        print("\n📊 测试检测解析...")
        
        # 模拟DETR风格输出
        batch_size = 1
        num_queries = 100
        num_classes = 10
        
        # 创建模拟检测结果
        pred_logits = torch.randn(batch_size, num_queries, num_classes + 1)  # +1 for background
        pred_boxes = torch.rand(batch_size, num_queries, 4)  # normalized coordinates
        
        detections = {
            'pred_logits': pred_logits,
            'pred_boxes': pred_boxes
        }
        
        # 解析检测结果
        image_path = Path("test_image.jpg")
        parsed_detections = inference_system._parse_detections(detections, image_path)
        
        print(f"📊 解析的检测数量: {len(parsed_detections)}")
        
        if parsed_detections:
            print("📋 检测结果示例:")
            for i, det in enumerate(parsed_detections[:3]):  # 显示前3个
                print(f"   {i+1}. 类别: {det['class_name']}, 置信度: {det['confidence']:.3f}")
        
        # 测试准确率计算
        print("\n📈 测试准确率计算...")
        accuracy_metrics = inference_system._calculate_accuracy_metrics(parsed_detections, image_path)
        
        print("📊 准确率指标:")
        for key, value in accuracy_metrics.items():
            if isinstance(value, dict):
                print(f"   {key}: {value}")
            elif isinstance(value, float):
                print(f"   {key}: {value:.4f}")
            else:
                print(f"   {key}: {value}")
        
        # 测试批量推理
        print("\n🚀 测试批量推理...")
        
        # 创建模拟图像张量
        batch_tensors = [torch.randn(1, 3, 512, 512) for _ in range(3)]
        batch_paths = [Path(f"test_image_{i}.jpg") for i in range(3)]
        
        try:
            batch_results = inference_system._inference_batch(batch_tensors, batch_paths)
            print(f"✅ 批量推理成功，处理了 {len(batch_results)} 张图片")
            
            # 显示批量结果统计
            total_detections = sum(len(r['detections']) for r in batch_results if r)
            avg_fps = np.mean([r['performance']['fps'] for r in batch_results if r])
            avg_mAP = np.mean([r['performance']['mAP'] for r in batch_results if r])
            
            print(f"📊 批量结果统计:")
            print(f"   总检测数: {total_detections}")
            print(f"   平均FPS: {avg_fps:.1f}")
            print(f"   平均mAP: {avg_mAP:.4f}")
            
        except Exception as e:
            print(f"❌ 批量推理失败: {e}")
        
        print("\n🎉 准确率计算测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_accuracy_calculation()
