2025-06-18T08:44:58.744Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-18T08:44:58.745Z [MCPServer] INFO: Parsed options: {}
2025-06-18T08:44:58.745Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-18T08:44:58.745Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-18T08:44:58.745Z [MCPServer] INFO: Current options: {}
2025-06-18T08:44:58.745Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-18T08:44:58.748Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-18T08:44:58.748Z [MCPServer] INFO: Starting relay server...
2025-06-18T08:44:59.881Z [MCPServer] INFO: Relay server started successfully
2025-06-18T08:44:59.881Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-18T08:45:03.932Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-18T08:45:03.932Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-18T08:50:53.055Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-18T08:50:53.056Z [MCPServer] INFO: Parsed options: {}
2025-06-18T08:50:53.056Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-18T08:50:53.057Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-18T08:50:53.057Z [MCPServer] INFO: Current options: {}
2025-06-18T08:50:53.057Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-18T08:50:53.059Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-18T08:50:53.060Z [MCPServer] INFO: Starting relay server...
2025-06-18T08:50:54.253Z [MCPServer] INFO: Relay server started successfully
2025-06-18T08:50:54.253Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-18T08:51:04.776Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-18T08:51:04.777Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-21T15:45:02.999Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-21T15:45:03.000Z [MCPServer] INFO: Parsed options: {}
2025-06-21T15:45:03.000Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-21T15:45:03.001Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-21T15:45:03.001Z [MCPServer] INFO: Current options: {}
2025-06-21T15:45:03.002Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-21T15:45:03.004Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-21T15:45:03.004Z [MCPServer] INFO: Starting relay server...
2025-06-21T15:45:04.198Z [MCPServer] INFO: Relay server started successfully
2025-06-21T15:45:04.198Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-21T15:45:06.232Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-21T15:45:06.232Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
