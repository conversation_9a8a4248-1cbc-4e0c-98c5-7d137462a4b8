# 转换经验

## 2025-06-19

- **字体兼容性**:
  - **问题**: `xelatex` 编译失败，提示 `\hei`, `\fs` 等字体命令未定义。
  - **原因**: `ctextemp_aas.cls` 使用了依赖于旧 CJK 宏包的字体命令，与 `ctextemp_template.tex` 中使用的 `xeCJK` 不兼容。
  - **解决方案**: 在 `ctextemp_aas.cls` 中添加了 `xeCJK` 兼容的字体定义，例如 `\newcommand{\hei}{\sffamily}`。

- **页眉设置**:
  - **问题**: 页眉未显示，或显示不正确，并出现 `Overfull \vbox` 警告。
  - **原因**:
    1. 首页页眉样式未被调用。
    2. `ctextemp_aas.cfg` 中的页眉定义使用了过时的、与 `xeCJK` 不兼容的字体命令和乱码。
    3. `ctextemp_aas.cls` 中为页眉预留的高度 (`\headheight`) 不足。
  - **解决方案**:
    1. 在 `ctextemp_template.tex` 的 `\maketitle` 命令后显式调用 `\thispagestyle{aas@firstheadings}`。
    2. 重写 `ctextemp_aas.cfg`，使用现代化、`xeCJK` 兼容的命令，并修正了文本内容。
    3. 在 `ctextemp_aas.cls` 中，将 `\headheight` 的值从 `6mm` 增加到 `12mm`。

- **引文格式**:
  - 将 `引文格式` 修改为 `\textbf{引文格式}`，使其加粗显示。