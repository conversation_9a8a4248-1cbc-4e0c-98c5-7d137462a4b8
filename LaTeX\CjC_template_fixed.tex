\documentclass[10.5pt,compsoc]{CjC}
\usepackage{xeCJ<PERSON>}
\usepackage{graphicx}
\usepackage{footmisc}
\usepackage{subfigure}
\usepackage{url}
\usepackage{multirow}
\usepackage[noadjust]{cite}
\usepackage{amsmath,amsthm}
\usepackage{amssymb,amsfonts}
\usepackage{booktabs}
\usepackage{color}
\usepackage{ccaption}
\usepackage{booktabs}
\usepackage{float}
\usepackage{fancyhdr}
\usepackage{caption}
\usepackage{xcolor,stfloats}
\usepackage{comment}
\setcounter{page}{1}
\graphicspath{{figures/}}
\usepackage{cuted}%flushend,
\usepackage{captionhack}
\usepackage{epstopdf}

\setCJKmainfont{SimSun}
\setCJKsansfont{SimHei}
\setCJKmonofont{FangSong}
\setCJKfamilyfont{zhhei}{SimHei}
\setCJKfamilyfont{zhfs}{FangSong}
\newcommand{\heiti}{\CJKfamily{zhhei}}
\newcommand{\fangsong}{\CJKfamily{zhfs}}

\headevenname{\mbox{\quad} \hfill  \mbox{\zihao{-5}{\heiti 计\quad \quad 算\quad \quad 机\quad \quad 学\quad \quad 报} \hspace {50mm} \mbox{2019 年}}}%
\headoddname{\fangsong ? \quad \hfill
模板保护作者信息，请勿删除}%

\renewcommand{\thefootnote}{\fnsymbol{footnote}}
\setcounter{footnote}{0}
\renewcommand\footnotelayout{\zihao{5-}}

\newtheoremstyle{mystyle}{0pt}{0pt}{\normalfont}{1em}{\bf}{}{1em}{}
\theoremstyle{mystyle}
\renewcommand\figurename{figure~}
\renewcommand{\thesubfigure}{(\alph{subfigure})}
\newcommand{\upcite}[1]{\textsuperscript{\cite{#1}}}
\renewcommand{\labelenumi}{(\arabic{enumi})}
\newcommand{\tabincell}[2]{\begin{tabular}{@{}#1@{}}#2\end{tabular}}
\newcommand{\abc}{\color{white}\vrule width 2pt}
\makeatletter
\renewcommand{\@biblabel}[1]{[#1]\hfill}
\makeatother
\setlength\parindent{2em}

\begin{document}
\hyphenpenalty=50000
\makeatletter
\newcommand\mysmall{\@setfontsize\mysmall{7}{9.5}}
\newenvironment{tablehere}
  {\def\@captype{table}}

\let\temp\footnote
\renewcommand \footnote[1]{\temp{\zihao{-5}#1}}


\thispagestyle{plain}%
\thispagestyle{empty}%
\pagestyle{CjCheadings}

\begin{table*}[!t]
\vspace {-13mm}
\begin{tabular}{p{168mm}}
\zihao{5-}
第??卷\quad 第?期 \hfill 计\quad 算\quad 机\quad 学\quad 报\hfill Vol. ??  No. ?\\
\zihao{5-}
20??年?月 \hfill CHINESE JOURNAL OF COMPUTERS \hfill ???. 20??\\
\hline\\[-4.5mm]
\hline\end{tabular}

\centering
\vspace {11mm}
{\zihao{2}\heiti 题目（中英文题目一致）要求为2号黑体(全角字符间不加空格，西文按习惯处理) }
\vskip 5mm

{\zihao{3}\fangsong
作者一$^{1)}$\quad  作者二$^{2),3)}$ \quad 作者三$^{3) }$($^*$姓名后加*号为通信作者)}

\vspace {5mm}
\zihao{6}{
$^{1)}$(单位全称 部门(系)全称, 省(直辖市)名 邮政编码)
*作者单位为6号宋体*单位
}

\zihao{6}{
$^{2)}$(单位全称 部门(系)全称, 省(直辖市)名 邮政编码)*中英文的单位名称、地址要求一致*
}

\zihao{6}{
$^{3)}$(单位全称 部门(系)全称, 省(直辖市)名 邮政编码)
}

\zihao{6}{\heiti
本文的投稿、审稿、编辑加工过程均在计算机学会开发的“稿件远程处理系统”中进行。送审前作者信息对审稿人完全保密。为保证评审的公正性，请作者在投稿时务必将作者的姓名、单位、基金号、个人简介等信息全部删除。
}

\vskip 5mm
{\centering
\begin{tabular}{p{160mm}}
\zihao{5-}{
\setlength{\baselineskip}{16pt}\selectfont{
\noindent{\heiti 摘\quad 要}\quad
*中文摘要内容写在此处(英文摘要要求反映同样的内容)，字体为小5号宋体。摘要应具独立性，要拥有与文献同等量的主要信息，以200-300字为宜。*摘要

\par}}\\[2mm]

\zihao{5-}{\noindent
{\heiti 关键词} \quad *关键词；关键词；关键词；关键词；关键词*  }
\\[2mm]
\zihao{5-}{{\heiti 中图分类号}	\quad
TP\rm{\quad \quad \quad     }
{\heiti DOI号}:\quad
*投稿时系统提供DOI号*}
\end{tabular}}

\vskip 7mm

\begin{center}
\zihao{4}{ {\textbf{Title *英文题目与中文题目一致)要求为4号Times New Roman,加粗* Title}}}\\
\vspace {5mm}
\zihao{5}{ {NAME Name-Name$^{1)}$ NAME Name$^{2)}$ NAME Name-Name$^{3)}$ *作者为5号Times
new Roman*Name
}}\\
\vspace {2mm}
\zihao{6}{{$^{1)}$(Department of ****, University, City ZipCode, China) *作者单位为6号Times
new Roman* Depart.Correspond}}

\zihao{6}{{$^{2)}$(Department of ****, University, City ZipCode)*中国作者请写明单位全称*}}

\zihao{6}{{$^{3)}$(Department of ****, University, City ZipCode, country)*国外作者请写明国家*}}



\end{center}

\begin{tabular}{p{160mm}}
\zihao{5}{
\setlength{\baselineskip}{18pt}\selectfont{
{\bf Abstract}\quad (\textbf{500英文单词为宜，内容包括中文摘要所有信息}).
*字体为Times new Roman,字号5号* Abstract
\par}}\\

\setlength{\baselineskip}{18pt}\selectfont{
\zihao{5}{\noindent Do not modify the amount of space before and after the artworks. One- or two-column format artworks are preferred. and Tables, create a new break line and paste the resized artworks where desired. Do not modify the amount of space before and after the artworks. One- or two-column format artworks are preferred. All Schemes, Equations, Figures, and Tables should be mentioned in the text consecutively and numbered with Arabic numerals, and appear below where they are mentioned for the first time in the main text. To insert Schemes, Equations, Figures, and Tables, create a new break line and paste the resized artworks where desired. Do not modify the amount of space before and after the artworks. One- or two-column format artworks are preferred.Do not modify the amount of space before and after the artworks. One- or two-column format artworks are preferred. and Tables, create a new break line and paste the resized artworks where desired. Do not modify the amount of space before and after the artworks. One- or two-column format artworks are preferred. All Schemes, Equations, Figures, and Tables should be mentioned in the text consecutively and numbered with Arabic numerals, and appear below where they are mentioned for the first time in the main text.

\vspace {5mm}
{\bf Keywords}\quad *中英文的关键词要求一一对应，\textbf{需要有英文的关键词});
key word; key word; key word* *字体为5号Times new Roman * Key words}\par}
\end{tabular}

\setlength{\tabcolsep}{2pt}
\begin{tabular}{p{0.05cm}p{16.15cm}}
\multicolumn{2}{l}{\rule[4mm]{40mm}{0.1mm}}\\[-3mm]
&\zihao{5-}
收稿日期: 2019-09-20; 修回日期: 2019-12-18. *投稿时请删除*. 本文的英文网络版发表于2020年2月25日.
基金项目: 国家自然科学基金(No.项目号); 国家高技术研究发展计划(863)(No.项目号); 国家重点基础研究发展计划(973)(No.项目号).
作者简介:姓名1(通信作者)(出生年--),性别,xx省xx市人,学位(或目前学位),职称,主要研究方向为*****、****.E-mail: **************.姓名2(出生年--),性别,xx省xx市人,学位(或目前学位),职称,主要研究方向为*****、****.E-mail: **************. 姓名3(出生年--),性别,xx省xx市人,学位(或目前学位),职称,主要研究方向为*****、****.E-mail: **************.(在读的作者请在“博士”后加“研究生”,“硕士”后加“研究生”)
\end{tabular}\end{table*}
\clearpage\clearpage
\begin{strip}
\vspace {-13mm}
\end{strip}
    \linespread{1.15}
\zihao{5}
\vskip 1mm
\section{\heiti 一级标题*标题为4号黑体*标题1}
\textbf{对投稿的格式要求}：

(1) 研究内容应属于计算机(重点是计算机科学理论、计算机硬件体系结构、计算机软件、人工智能、图形图像与模式识别、多媒体与人机交互、信息安全、数据库、计算机网络、计算机应用等)领域(交叉学科请侧重于计算机科学与技术)。

(2) 论文应是作者未发表过的研究成果(博士、硕士论文工作除外)，应包含完整的创新思路、关键技术、实验验证(或理论证明)、结论等，系统实现或实际应用关键技术应详细，以便他人能够重复实验，理论推导应严谨、正确，实验或应用研究应有全貌、细致的数据对比分析。

(3) 论文撰写规范请参考本刊网站“投稿指南”栏目。

\subsection{\heiti 二级标题 *标题为5号黑体*标题2}
\subsubsection{三级标题 *标题为5号宋体*标题3}
*正文内容, 字体为5号宋体*

\textbf{论文提交时的格式要求}

(1) 按模板要求的格式认真撰写，确保齐全、正确、无遗漏。

(2) 语言通顺、流畅。中、英文摘要内容应基本一致，无明显语法错误，避免使用不规范的图、表和公式。

(3) 专业术语应规范、统一，前后一致，公式推导正确无误。

{\heiti\textbf{定理1}.}\quad ******. *定理叙述.*

[“引理”、“推论”、“假设”、“注”等的编排格式与“定理”相同，具体可参见附录中定理证明的格式规范]

{\fangsong 证明}.\quad  *证明过程.* [“注 x”等的编排格式与此相同]

\rightline {证毕.}


\begin{figure}[htbp]
\centerline{\includegraphics[width=3.15in,height=1.98in]{CJC1.pdf}}
\caption{图X\quad  图片说明 *字体为小5号,图片应为黑白图，图中的分图要有分图说明*}
\label{fig1}
\end{figure}

\begin{table}[htbp]
\centering {\zihao{5}\heiti 表X\quad 表说明} *表说明在表格上方*
\vspace {-2.5mm}
\begin{center}
\begin{tabular}{ll}
\toprule
*示例表格*&*第1列为表头,表头要居中* \\
\hline
 & \\
 & \\
 & \\
 & \\
\bottomrule
\end{tabular}
\label{tab1}
\end{center}
\end{table}

{\heiti 算法X.}\quad 算法名称.

{\zihao{5-}*算法的数学描述的字体为小5号宋体, IF、THEN、ELSE等关键词全部用大写字母, 函数和变量用斜体*}


{\heiti 算法\textbf{Y}}.\quad 算法名称.
\zihao{5-}{

\noindent 输入：{\ldots} {\ldots}

\noindent 输出：{\ldots} {\ldots}

*算法的数学描述和伪代码的字体为小5号宋体, IF、THEN、ELSE等关键词全部用大写字母, 函数和变量用斜体*}

\vspace {3mm}
\zihao{5}{
\noindent {\heiti 致\quad 谢}\quad {\fangsong *致谢内容.* 致谢}}


\vspace {5mm}
\centerline
{\zihao{5}
{\heiti 参~考~文~献}}

\begin{thebibliography}{99}
\zihao{5-} \addtolength{\itemsep}{-1em}
\vspace {1.5mm}

\bibitem[1]{1}
网络媒体的参考文献(文献类型标识J/OL). (The Cooperative
Association for Internet Data Analysis(CAIDA),http://www.caida.org/data
2010,7,18) \textbf{*网络文献请注意给出可访问的网址和访问日期*}\footnote{The Cooperative Association for Internet Data
Analysis (CAIDA), http://www.caida.org/data 2010, 7, 18}

\bibitem[2]{2} 论文的参考文献请尽量引用公开发表的文章，中英文对照，格式如[3]

\bibitem[3]{3} Zhou Yong-Bin, Feng Deng-Guo. Design and analysis of cryptographic

\noindent {\zihao{5}\bf{附录X}.}

{\zihao{5-}\setlength\parindent{2em}
*\textbf{附录内容}写在此处，字体为小5号宋体。附录内容包括\textbf{详细的定理证明、公式推导、原始数据}等*}

protocols for RFID. Chinese Journal of Computers, 2006, 29(4): 581-589 (in
Chinese) \newline
(周永彬, 冯登国. RFID安全协议的形式化分析. 计算机学报, 2006, 29(4): 581-589)

\bibitem[4]{4} 期刊、专著、论文集等文献的著录规则请参照本刊网站

\bibitem[5]{5} 作者(所有作者姓在前名在后, 名可缩写).
篇名(英文篇名第1个词首字母大写，其余为小写). 期刊名(全称), 年,
卷(期): 页码 \textbf{*期刊文献的格式*}

\bibitem[6]{6}作者.
篇名(英文篇名第1个词首字母大写，其余为小写)//Proceedings of
the {\ldots} (会议全称). 会议的召开城市, 会议召开所在国家, 年: 页码
\textbf{*会议论文集的格式*}

\bibitem[7]{7}作者. 篇名(英文篇名第1个词首字母大写, 其余为小写):
其它题名信息//原文献作者. 文献题名. 出版地: 出版者, 出版年: 页码
\textbf{*论文集格式*}

\bibitem[8]{8}作者. 书名: 其它题名信息(版次). 卷(册). 出版地: 出版者,
出版年 \textbf{*专著格式*}

\bibitem[9]{9}作者. 论文题目[博士学位论文/硕士学位论文]. 单位地址,单位名称, 年
\textbf{*学位论文的格式*}

\bibitem[10]{10}作者. 报告题目(英文报告题目第1个词首字母大写，其余为小写). 单位地址: 单位名称,
技术报告, 年 \textbf{*科技报告*}

\bibitem[11]{11}专利申请者. 专利题名:专利国别,专利号,公告日期
\textbf{*专利文献*}
\end{thebibliography}

\begin{biography}[yourphotofilename.jpg]
\noindent
\textbf{First A. Author}\ \ *计算机学报的第1作者需提供免冠照片的黑白图片，尺寸为1寸。英文作者简介内容包括：姓名,学位(或目前学位),职称,主要研究方向(\textbf{请务必提供作者简介的英文翻译}).*
*字体为小5号Times New Roman*

\end{biography}

\begin{biography}[yourphotofilename.jpg]
\noindent
\textbf{Second B. Author} *英文作者简介内容包括：姓名,学位(或目前学位),职称,主要研究方向(\textbf{请务必提供作者简介的英文翻译})。*
*字体为小5号Times New Roman*
\end{biography}

\end{document}
\zihao{5}
\noindent \textbf{Background}

\zihao{5-}{
\setlength\parindent{2em}
*此部分内容为\textbf{英文}，字体为小5号Times New Roman。*

此部分长度为400单词左右的英文背景介绍。内容包括：

该研究领域的重要性，解决什么问题。

目前的研究进展到什么程度。

本文的贡献到什么程度。

本文的研究目标。

目标的应用。

该研究群体和其它相关研究成果。

本文的成果是“国家重点基础研究发展计划(973计划)”或“国家高技术研究发展计划(863计划)”等重大项目，请注明这些项目的英文名称，要求写准确。}