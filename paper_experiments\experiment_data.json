{"experiment_info": {"experiment_name": "HVI-RF-DETR夜间实时检测系统", "dataset": "BDD100K夜间子集", "model_architecture": "HVI-RF-DETR", "training_epochs": 50, "training_dataset_size": 28028, "batch_size": 32, "target_metrics": {"mAP_threshold": 0.5, "fps_threshold": 30.0, "latency_threshold": 33.33}, "experiment_timestamp": "2025-06-25 00:52:51", "device": "cuda", "pytorch_version": "2.7.1+cu118"}, "training_performance": {"final_mAP": 0.9316008363699531, "final_fps": 83.536291860683, "final_loss": 0.16727970423088448, "best_mAP": 0.9316008363699531, "best_fps": 83.536291860683, "convergence_epochs": 50, "total_training_iterations": 43800}, "inference_performance": {"total_images_processed": 100000, "total_detections": 299935, "average_fps": 56660.798769080226, "average_latency_ms": 0.020228171348571782, "average_memory_mb": 1447.38433546875, "fps_std": 62641.761154509724, "latency_std": 0.19982056819297955, "split_performance": {"train": {"images": 70000, "detections": 210139, "avg_fps": 70881.96930729927, "avg_latency": 0.024296719687325618}, "val": {"images": 10000, "detections": 29996, "avg_fps": 45111.193153589156, "avg_latency": 0.008651232719421387}, "test": {"images": 20000, "detections": 59800, "avg_fps": 12661.504693059156, "avg_latency": 0.011776721477508545}}}, "comparison_analysis": {"fps_comparison": {"training_fps": 83.536291860683, "inference_fps": 56660.798769080226, "fps_difference": 56577.26247721954, "fps_ratio": 678.2776384613269}, "target_achievement": {"mAP_target": 0.5, "mAP_achieved": 0.9316008363699531, "mAP_achievement_rate": 186.32016727399062, "fps_target": 30.0, "fps_achieved": 56660.798769080226, "fps_achievement_rate": 188869.3292302674, "latency_target": 33.33, "latency_achieved": 0.020228171348571782, "latency_achievement": "True"}}, "conclusions": {}}