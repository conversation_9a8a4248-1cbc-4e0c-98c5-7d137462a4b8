国内外研究现状分析和发展趋势：

低光环境下的行人实时检测是计算机视觉领域的长期挑战。传统方法主要依赖于红外成像或增强可见光图像后进行检测。近年来，随着深度学习技术的飞速发展，基于深度学习的目标检测算法在白天场景取得了显著成果，但在夜间等复杂光照条件下性能仍有待提升。
历经五十余年发展，低光环境下的实时目标检测技术路径演进可划分为四个阶段：传统图像增强与手工特征时代（1969-2015）、深度学习初探与两阶段检测器（2015-2022）、专用模型与端到端优化（2022-2024）、实时Transformer与物理模型融合（2024-2025）。每个阶段的技术突破均伴随着底层理论创新与工程约束的博弈平衡。

第一阶段：传统图像增强与手工特征时代（1969-2015）
早期研究以图像增强为核心，通过物理模型和统计方法提升图像可见性。直方图均衡化[1]（HE） 由Rosenfeld等人于1969年提出，通过像素重分布增强对比度，但易放大噪声。
Retinex理论[2]由Land和McCann于1971年提出，模拟人眼视觉系统分解反射分量与光照分量，但计算复杂度高且依赖参数调优。
1994 年，Karel Zuiderveld在《Graphics Gems IV》发表《Contrast Limited Adaptive Histogram Equalization》[3]，该文详细介绍了 CLAHE 的原理和实现，包括将图像分割成小块，对每小块进行直方图均衡化，并通过限制对比度来避免过度增强噪声等，但这使得计算复杂度较高且引入了伪影。
特征提取方面，方向梯度直方图（HOG）[4] 和 局部二值模式（LBP）[5] 成为主流手工特征，但对低光模糊和噪声敏感。2010年后，多光谱融合初步探索，如结合可见光与远红外（FIR）传感器数据，利用热辐射特性定位行人轮廓。传统方法因特征表达能力有限，在动态场景中漏检率高达40%以上。

第二阶段：深度学习初探与两阶段检测器（2016-2022）
深度学习推动行人检测进入新阶段。Faster R-CNN[6]通过区域建议网络（RPN）生成候选框，结合VGG16骨干网络实现高精度检测，但推理速度仅5 FPS。
Ren等人于2015年发表的突破性论文《Faster R-CNN: Towards Real-Time Object Detection with Region Proposal Networks》[7]，该模型通过共享卷积特征策略，将区域建议网络（RPN）与Fast R-CNN检测网络有机融合，在PASCAL VOC数据集上以5FPS的推理速度实现73.2%的mAP。其创新性地采用滑动窗口机制生成多尺度锚框（anchor boxes），通过端到端训练同时优化区域建议的objectness score和边界框回归参数，使得候选框生成耗时从Fast R-CNN的2秒/图锐减至10ms/图，标志着两阶段检测器进入实用化阶段。但两阶段级联架构导致至少两次特征重计算，VGG16骨干网络参数量达1.38亿，并且基于人工设计的锚框尺寸对极端长宽比行人（如骑自行车者）适应性差。
SSD[8]采用多尺度特征图直接预测边界框，速度提升至30 FPS，但在低光小目标场景下精度骤降。
针对低光特性，跨模态迁移学习成为研究热点。KAIST数据集[9]（含白天-夜间配对数据）被广泛用于可见光模型向红外模态的迁移。

第三阶段：专用模型与端到端优化（2022-2024）
此阶段技术突破集中在自适应增强、注意力机制和轻量化设计：
1.自适应增强网络：
IA-YOLO[10]引入可微分ISP模块，将RAW数据直接输入检测网络，减少图像增强-检测的误差累积。但存在超参数调整复杂性高与对特定天气条件的过度优化的问题。
GDIP-YOLO[11]通过生成对抗网络（GAN）动态生成增强参数，在极端低光下PSNR提升4.2 dB。GDIP-YOLO引入了额外的图像处理模块（GDIP），并且通过门控机制动态调整图像增强参数。但这些操作增加了模型的计算复杂度使实时性降低。
RetinaHA-YOLOv8集成RetinexFormer作为前置处理模块，恢复低光图像细节，AP提升5.4%。虽然 RetinaHA-YOLOv8 引入了在线重参数化卷积技术来优化推理速度，但这种优化仍然无法完全弥补额外模块带来的计算开销。
2.注意力机制革新：
GAM注意力[12]在YOLOv8中集成通道-空间双重注意力，抑制背景干扰，使夜间小目标mAP@0.5提升3.4%。GAM注意力机制在夜间小目标检测任务中表现出色，通过通道-空间双重注意力显著提升了检测性能。然而，其计算复杂度增加、模型泛化能力的不确定性、对输入数据的依赖、模型的可解释性降低、超参数调整的复杂性以及对特定数据集的过度优化等缺点，限制了其在实时场景下的应用。
ECA模块结合高效通道注意力与双向特征金字塔（BIFPN），在KAIST数据集上实现86% mAP，推理时间仅0.0081秒。但ECA模块未集成可微分ISP，RAW数据中的噪声在低照度下会被放大。
3.轻量化与多任务融合：
基于参数重参化（RepVGG Block）的YOLOv6-nano[13]通过结构重设计压缩模型至1.1M参数量，在Jetson Xavier NX边缘设备实现187 FPS推理速度。但研究发现，RepVGG的层融合机制会削弱模型对遮挡目标的特征表达能力
PP-YOLOE-tiny[14]采用多任务蒸馏框架，在保持2.3M参数量的同时，通过PANet-FPN增强多尺度特征融合，于VisDrone2021数据集实现39.2% mAP（CVPR2023论文数据），较基准模型降低8.3%误检率。

第四阶段：实时Transformer与物理模型融合（2024-2025）
1.实时Transformer检测：
LW-DETR[15]（2024）采用ViT-L/16编码器提取多尺度特征，配合可变形注意力解码器实现无锚框检测，在COCO val2017数据集上取得56.1 mAP@640px，Tesla V100平台测得113 FPS，相较同精度YOLOv8-x6模型推理延迟降低31.2%。其创新的动态IoU加权定位损失使边界框CIoU指标提升11.7%（COCO test-dev）。
2.新型色域模型：
HVI色域增强[16]（2025）提出HVI（横向-纵向-强度）色彩空间，通过坐标极化将HSV颜色模型的颜色属性从极坐标转换到笛卡尔坐标系，解决了HSV色域增强中色度不连续导致的红色伪影，通过强度坍缩函数自适应抑制低光区域噪声有效去除了黑色噪声伪影。LOLv1和LOLv2数据集上，HVI-CIDNet在所有指标上表现最佳。与基于RGB 的GSAD方法相比，参数仅为其10.8%，性能却更优。
3.扩散模型应用：
DarkVision-Net[17]通过物理引导扩散将光照传输模型作为扩散过程约束条件，配合多模态对比预训练（红外-可见光对齐），突破传统数据驱动模型的物理逻辑缺失瓶颈。
LightDiff[18]通过物理约束扩散模型与红外-可见光跨模态对比学习，突破传统成像极限，实现单光子级至强光环境的全时域高保真重建。

参考文献
[1]	Chang Y, Jung C, Ke P, et al. Automatic Contrast-Limited Adaptive Histogram Equalization With Dual Gamma Correction[J]. IEEE Access, 2018, 6: 11782-11792.
[2]	McCann J. Retinex Theory[M]//Shamey R. Encyclopedia of Color Science and Technology. Berlin, Heidelberg: Springer, 2020: 1-8.
[3]	Zuiderveld K. Contrast limited adaptive histogram equalization[M]//Graphics gems IV. USA: Academic Press Professional, Inc., 1994: 474-485.
[4]	Dalal N, Triggs B. Histograms of oriented gradients for human detection[C]//2005 IEEE Computer Society Conference on Computer Vision and Pattern Recognition (CVPR’05): vol 1. 2005: 886-893 vol 1.
[5]	Ojala T, Pietikäinen M, Harwood D. A comparative study of texture measures with classification based on featured distributions[J]. Pattern Recognition, 1996, 29(1): 51-59.
[6]	Ren S, He K, Girshick R, et al. Faster R-CNN: Towards Real-Time Object Detection with Region Proposal Networks[J]. IEEE Transactions on Pattern Analysis and Machine Intelligence, 2017, 39(6): 1137-1149.
[7]	Ren S, He K, Girshick R, et al. Faster R-CNN: Towards Real-Time Object Detection with Region Proposal Networks[M]. arXiv, 2016.
[8]	Liu W, Anguelov D, Erhan D, et al. SSD: Single Shot MultiBox Detector[C]//Leibe B, Matas J, Sebe N, et al. Computer Vision – ECCV 2016. Cham: Springer International Publishing, 2016: 21-37.
[9]	Hwang S, Park J, Kim N, et al. Multispectral pedestrian detection: Benchmark dataset and baseline[C]//2015 IEEE Conference on Computer Vision and Pattern Recognition (CVPR). 2015: 1037-1045.
[10]	Liu W, Ren G, Yu R, et al. Image-Adaptive YOLO for Object Detection in Adverse Weather Conditions[M]. arXiv, 2022.
[11]	Kalwar S, Patel D, Aanegola A, et al. GDIP: Gated Differentiable Image Processing for Object-Detection in Adverse Conditions[M]. arXiv, 2022.
[12]	Liu Y, Shao Z, Hoffmann N. Global Attention Mechanism: Retain Information to Enhance Channel-Spatial Interactions[M]. arXiv, 2021.
[13]	Li C, Li L, Jiang H, et al. YOLOv6: A Single-Stage Object Detection Framework for Industrial Applications[M]. arXiv, 2022.
[14]	Xu S, Wang X, Lv W, et al. PP-YOLOE: An evolved version of YOLO[M]. arXiv, 2022.
[15]	Chen Q, Su X, Zhang X, et al. LW-DETR: A Transformer Replacement to YOLO for Real-Time Detection[EB]//arXiv.org. (2024-06-05).
[16]	Yan Q, Feng Y, Zhang C, et al. HVI: A New Color Space for Low-light Image Enhancement[EB]//arXiv.org. (2025-02-27).
[17]	Jin S, Yu B, Jing M, et al. DarkVisionNet: Low-Light Imaging via RGB-NIR Fusion with Deep Inconsistency Prior[J]. Proceedings of the AAAI Conference on Artificial Intelligence, 2022, 36(1): 1104-1112.
[18]	Li J, Li B, Tu Z, et al. Light the Night: A Multi-Condition Diffusion Framework for Unpaired Low-Light Enhancement in Autonomous Driving[C]//Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 2024: 15205-15215.

