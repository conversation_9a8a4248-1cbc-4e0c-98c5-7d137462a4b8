2025-06-25 00:51:10,608 - INFO - 📦 加载检查点: unified_runs/hvi_rf_detr_28k_20250624_162451/checkpoints/best_checkpoint.pth
2025-06-25 00:51:11,151 - INFO - ✅ 检查点加载成功:
2025-06-25 00:51:11,153 - INFO -    📊 Epoch: 49
2025-06-25 00:51:11,153 - INFO -    📈 mAP: 0.932
2025-06-25 00:51:11,153 - INFO -    🚀 FPS: 83.5
2025-06-25 00:51:11,153 - INFO -    🏗️ 架构: HVI-RF-DETR-Unified-28K
2025-06-25 00:51:11,158 - INFO - 🚀 全局数据集推理系统初始化完成
2025-06-25 00:51:11,158 - INFO - 📁 实验目录: inference_runs\global_inference_20250625_005110
2025-06-25 00:51:11,158 - INFO - 🎯 目标性能: 30.0 FPS, 33.3ms延迟
2025-06-25 00:51:11,158 - INFO - 📊 TensorBoard: tensorboard --logdir inference_runs\global_inference_20250625_005110\tensorboard
2025-06-25 00:51:11,191 - ERROR - ❌ 批量推理失败: Input type (torch.FloatTensor) and weight type (torch.cuda.FloatTensor) should be the same or input should be a MKLDNN tensor and weight is a dense tensor
2025-06-25 00:51:11,191 - ERROR - ❌ 推理失败 test_image_0.jpg: Input type (torch.FloatTensor) and weight type (torch.cuda.FloatTensor) should be the same or input should be a MKLDNN tensor and weight is a dense tensor
2025-06-25 00:51:11,192 - ERROR - ❌ 推理失败 test_image_1.jpg: Input type (torch.FloatTensor) and weight type (torch.cuda.FloatTensor) should be the same or input should be a MKLDNN tensor and weight is a dense tensor
2025-06-25 00:51:11,192 - ERROR - ❌ 推理失败 test_image_2.jpg: Input type (torch.FloatTensor) and weight type (torch.cuda.FloatTensor) should be the same or input should be a MKLDNN tensor and weight is a dense tensor
