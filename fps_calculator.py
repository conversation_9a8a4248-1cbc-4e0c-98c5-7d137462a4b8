#!/usr/bin/env python3
"""
FPS计算器 - 准确计算所有相关数值
"""

import numpy as np

def calculate_all_fps_metrics():
    """计算所有FPS相关指标"""
    
    print("🧮 FPS精确计算器")
    print("=" * 60)
    
    # 原始数据
    batch_size = 96
    target_fps = 30.0
    training_fps = 83.5
    
    # 原始推理FPS（批处理FPS）
    original_fps = {
        'train': 70882.0,
        'val': 45111.2, 
        'test': 12661.5
    }
    
    images = {
        'train': 70000,
        'val': 10000,
        'test': 20000
    }
    
    print("1. 原始批处理FPS:")
    for split, fps in original_fps.items():
        print(f"   {split}: {fps:,.1f}")
    
    # 修正后的单张图片FPS
    corrected_fps = {}
    for split, fps in original_fps.items():
        corrected_fps[split] = fps / batch_size
    
    print(f"\n2. 修正后单张图片FPS (除以批大小{batch_size}):")
    for split, fps in corrected_fps.items():
        print(f"   {split}: {fps:.1f}")
    
    # 计算加权平均FPS
    total_weighted_fps = 0
    total_images = 0
    for split in corrected_fps:
        total_weighted_fps += corrected_fps[split] * images[split]
        total_images += images[split]
    
    avg_fps = total_weighted_fps / total_images
    print(f"\n3. 加权平均FPS: {avg_fps:.1f}")
    
    # 与目标FPS对比
    fps_ratio = avg_fps / target_fps
    print(f"\n4. 性能对比:")
    print(f"   目标FPS: {target_fps}")
    print(f"   实际FPS: {avg_fps:.1f}")
    print(f"   超目标倍数: {fps_ratio:.1f}x")
    
    # 训练vs推理FPS对比
    training_vs_inference_ratio = avg_fps / training_fps
    print(f"\n5. 训练vs推理FPS对比:")
    print(f"   训练FPS: {training_fps}")
    print(f"   推理FPS: {avg_fps:.1f}")
    print(f"   推理提升倍数: {training_vs_inference_ratio:.1f}x")
    
    # 批处理效应分析
    batch_increase_ratio = 96 / 32
    fps_increase_ratio = avg_fps / training_fps
    efficiency_ratio = fps_increase_ratio / batch_increase_ratio
    
    print(f"\n6. 批处理效应分析:")
    print(f"   批大小增加: 32 → 96 ({batch_increase_ratio:.1f}x)")
    print(f"   FPS增加: {training_fps} → {avg_fps:.1f} ({fps_increase_ratio:.1f}x)")
    print(f"   效率比: {efficiency_ratio:.2f} (>1表示超线性加速)")
    
    # 计算延迟
    avg_latency_ms = 1000 / avg_fps
    print(f"\n7. 延迟计算:")
    print(f"   平均延迟: {avg_latency_ms:.1f}ms")
    print(f"   目标延迟: ≤33.3ms")
    print(f"   是否达标: {'✅' if avg_latency_ms <= 33.3 else '❌'}")
    
    # FPS标准差计算
    fps_values = list(corrected_fps.values())
    fps_std = np.std(fps_values)
    fps_cv = fps_std / avg_fps * 100
    
    print(f"\n8. FPS变异性分析:")
    print(f"   FPS标准差: {fps_std:.1f}")
    print(f"   变异系数: {fps_cv:.1f}%")
    
    # 数据分割间差异
    max_fps = max(fps_values)
    min_fps = min(fps_values)
    fps_range_ratio = max_fps / min_fps
    
    print(f"\n9. 数据分割间差异:")
    print(f"   最高FPS: {max_fps:.1f} (train)")
    print(f"   最低FPS: {min_fps:.1f} (test)")
    print(f"   差异倍数: {fps_range_ratio:.1f}x")
    
    # 返回所有计算结果
    results = {
        'batch_size': batch_size,
        'corrected_fps': corrected_fps,
        'average_fps': avg_fps,
        'target_fps': target_fps,
        'fps_ratio_vs_target': fps_ratio,
        'training_fps': training_fps,
        'fps_ratio_vs_training': training_vs_inference_ratio,
        'average_latency_ms': avg_latency_ms,
        'fps_std': fps_std,
        'fps_cv': fps_cv,
        'fps_range_ratio': fps_range_ratio,
        'batch_efficiency_ratio': efficiency_ratio
    }
    
    return results

if __name__ == "__main__":
    results = calculate_all_fps_metrics()
    
    print(f"\n🎯 关键数值摘要:")
    print(f"   平均FPS: {results['average_fps']:.1f}")
    print(f"   超目标: {results['fps_ratio_vs_target']:.1f}x")
    print(f"   vs训练: {results['fps_ratio_vs_training']:.1f}x")
    print(f"   平均延迟: {results['average_latency_ms']:.1f}ms")
    print(f"   效率比: {results['batch_efficiency_ratio']:.2f}")
