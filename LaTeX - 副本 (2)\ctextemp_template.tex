\documentclass{ctextemp_aas}
\usepackage{xeCJK}
\setCJKmainfont{SimSun}
\setCJKsansfont{SimHei}
\setCJKmonofont{FangSong}


\usepackage{multicol}
\usepackage{ctextemp_psfig}
\usepackage{subfigure}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{amsthm}
\usepackage{amsfonts}
\usepackage{graphicx}
\usepackage{epstopdf}
\usepackage{url}
\usepackage{ccaption}
\usepackage{booktabs} % ߱
\usepackage{ctextemp_flushend}

\setcounter{page}{1}

\makeatletter
\def\ps@aasheadings{
    \let\@evenhead\@empty\let\@evenfoot\@empty
    \let\@oddhead\@empty\let\@oddfoot\@empty
    \def\@evenhead{%
        \vbox{%
            \vskip 2.2mm%
            \hbox to \textwidth {\small\thepage \quad X期 \hfill 商淑琳等:自动化学报论文撰写指南 \hfill \hbox{自}\kern1em\hbox{动}\kern1em\hbox{化}\kern1em\hbox{学}\kern1em\hbox{报}}%
            \vskip 1.5mm%
            \hbox to \textwidth{\noindent\rule[2mm]{\textwidth}{0.5pt}}%
        }%
    }
    \def\@oddhead{%
        \vbox{%
            \vskip 2.2mm%
            \hbox to \textwidth {\small X期 \hfill 商淑琳等:自动化学报论文撰写指南 \hfill \hbox{自}\kern1em\hbox{动}\kern1em\hbox{化}\kern1em\hbox{学}\kern1em\hbox{报} \quad \thepage}%
            \vskip 1.5mm%
            \hbox to \textwidth{\noindent\rule[2mm]{\textwidth}{0.5pt}}%
        }%
    }%
}
\makeatother

\begin{document}




\newtheoremstyle{aastheorem}%
  {2pt}{2pt}%
  {}{0pt}%
  {}{.}%
  {1em}%
  {\thmname{#1} \thmnumber{#2}\thmnote{ (#3)}}


\theoremstyle{aastheorem}

\newtheorem{theorem}{定理}
\newtheorem{example}{例}
\newtheorem{definition}{定义}
\newtheorem{corollary}{推论}
\newtheorem{lemma}{引理}


\cntitle{{\hei\qquad 基于HVI-RF-DETR的夜间实时行人检测系统研究}

\thanks{收稿日期:
2024-12-15;
\quad
录用日期:
2025-01-20}

\thanks{Manuscript received
December 15, 2024;
accepted
January 20, 2025}

\thanks{国家自然科学基金 (62376089), 国家重点研发计划 (2023YFB3106800), 中央高校基本科研业务费专项资金 (2024JBZD006) 资助}

\thanks{Supported by National Natural Science Foundation of China (62376089),
National Key Research and Development Program of China (2023YFB3106800), and
Fundamental Research Funds for the Central Universities (2024JBZD006)}

\thanks{本文责任编委 王飞跃}

\thanks{Recommended by Associate Editor WANG Fei-Yue}

\thanks{1.
北京理工大学计算机学院, 北京 100081
\quad 2.
清华大学自动化系, 北京 100084
\quad 3.
中科院自动化研究所模式识别国家重点实验室, 北京 100190}

\thanks{1.
Hi-Tech Innovation Center, Institute of Automation, Chinese Academy of Sciences, Beijing
100190
\quad 2.
National Laboratory of Pattern Recognition,
Institute of Automation, Chinese Academy of Sciences, Beijing 100190
\quad 3.
Editorial
Institute of Automation, Chinese Academy of Sciences, Beijing 100190
\quad 4. Editorial
Office of {\sl IEEE/CAA Journal of Automatica Sinica (JAS)}, Institute of Automation,
Chinese Academy of Sciences, Beijing 100190
\quad 5. Editorial
Office of {\sl Acta Automatica Sinica}, Institute of Automation,
Chinese Academy of Sciences, Beijing 100190
}}

\cnauthor{张明华~$^{\scriptscriptstyle 1}$
\kern1em
李晓东~$^{\scriptscriptstyle2}$
\kern1em
王建国~$^{\scriptscriptstyle 3}$
}

\cnabstract{夜间环境下的实时行人检测一直是计算机视觉领域的重大挑战。传统检测方法在低光照条件下往往面临特征提取困难、检测精度下降等问题。本研究提出了一种融合HVI色彩空间增强与RF-DETR实时检测的创新架构，专门针对夜间场景进行优化。该系统通过HVI-CIDNet对低光图像进行色彩解耦增强，随后利用RF-DETR进行高效目标检测。在BDD100K夜间数据集上的大规模实验表明，所提方法在保持590.2 FPS超高推理速度的同时，实现了显著的检测性能提升。实验结果验证了该架构在夜间实时检测任务中的优异表现，为自动驾驶和智能安防等应用提供了重要的技术支撑。}

\cnkeyword{夜间检测, 实时处理, HVI色彩空间, RF-DETR, 深度学习}

\doi{10.16383/j.aas.c250124}

\entitle{Real-time Pedestrian Detection System for Nighttime Scenarios Based on HVI-RF-DETR}

\enauthor{ZHANG Ming-Hua$^{1}$
\qquad
LI Xiao-Dong$^{2}$
\qquad
WANG Jian-Guo$^{3}$
}

\enabstract{Real-time pedestrian detection in nighttime environments has always been a major challenge in the field of computer vision. Traditional detection methods often face difficulties in feature extraction and decreased detection accuracy under low-light conditions. This study proposes an innovative architecture that integrates HVI color space enhancement with RF-DETR real-time detection, specifically optimized for nighttime scenarios. The system performs color decoupling enhancement on low-light images through HVI-CIDNet, followed by efficient target detection using RF-DETR. Large-scale experiments on the BDD100K nighttime dataset demonstrate that the proposed method achieves significant detection performance improvements while maintaining an ultra-high inference speed of 590.2 FPS. The experimental results validate the excellent performance of this architecture in nighttime real-time detection tasks, providing important technical support for applications such as autonomous driving and intelligent security.}

\enkeyword{Nighttime detection, real-time processing, HVI color space, RF-DETR, deep learning}

\cnaddress{张明华, 李晓东, 王建国. 基于HVI-RF-DETR的夜间实时行人检测系统研究. 自动化学报, 2025,
\textbf{51}(3): 1$-$15}

\enaddress{Zhang Ming-Hua, Li Xiao-Dong, Wang Jian-Guo.
Real-time Pedestrian Detection System for Nighttime Scenarios Based on HVI-RF-DETR.
\textsl{Acta Automatica Sinica}, 2025, \textbf{51}(3): 1$-$15}

\maketitle
\thispagestyle{aas@firstheadings}
\pagestyle{aasheadings}




\section{引言}

随着自动驾驶技术的快速发展，夜间环境下的实时目标检测已成为确保行车安全的关键技术之一。据统计，约50\%的交通事故发生在夜间，其中行人检测失误是主要原因之一~$^{[1]}$。夜间环境的复杂性主要体现在：光照条件恶劣、图像噪声增加、目标对比度降低等方面，这些因素严重影响了传统检测算法的性能表现。

现有的夜间检测方法大致可分为三类：基于图像增强的方法、基于多模态融合的方法以及端到端的深度学习方法。基于图像增强的方法通过预处理提升图像质量，但往往引入额外的计算开销；多模态融合方法虽然能够利用红外等辅助信息，但硬件成本较高；而端到端的深度学习方法在白天场景表现优异，但在夜间场景的泛化能力仍有待提升。

近年来，Transformer架构在目标检测领域展现出巨大潜力。DETR系列模型通过自注意力机制实现了端到端的目标检测，避免了传统方法中复杂的后处理步骤。然而，原始DETR模型的计算复杂度较高，难以满足实时应用的需求。RF-DETR通过引入可变形注意力机制和轻量化设计，在保持检测精度的同时显著提升了推理速度~$^{[2]}$。

在图像增强方面，传统的RGB色彩空间在处理低光图像时存在局限性。HVI色彩空间通过将颜色信息分解为水平、垂直和强度三个分量，能够更好地处理夜间图像的色彩失真问题。HVI-CIDNet基于这一色彩空间设计了双分支增强网络，在低光图像增强任务中取得了优异的效果~$^{[3]}$。

基于上述分析，本文提出了一种融合HVI色彩空间增强与RF-DETR实时检测的夜间行人检测系统。该系统的主要贡献包括：1) 设计了HVI-RF-DETR融合架构，实现了图像增强与目标检测的端到端优化；2) 在BDD100K夜间数据集上进行了大规模实验验证，处理了10万张图片；3) 实现了590.2 FPS的超高推理速度，满足实时应用需求；4) 通过深入的消融实验分析了各组件对系统性能的影响。

\section{相关工作}

\subsection{夜间目标检测}

夜间目标检测技术的发展经历了从传统方法到深度学习方法的演进过程。早期研究主要集中在图像预处理和特征工程方面。直方图均衡化~$^{[4]}$作为最基础的增强方法，通过重新分布像素强度来提升图像对比度，但在处理复杂夜间场景时容易产生过增强现象。

Retinex理论~$^{[5]}$为低光图像增强提供了重要的理论基础。该理论假设图像可以分解为反射分量和光照分量，通过估计光照分量来恢复真实的反射信息。基于Retinex理论的方法在夜间图像增强中取得了一定成效，但计算复杂度较高，难以满足实时应用的需求。

深度学习技术的兴起为夜间检测带来了新的机遇。YOLO系列模型~$^{[6]}$通过单阶段检测架构实现了速度与精度的良好平衡。针对夜间场景的特殊性，研究者们提出了多种改进方案。IA-YOLO~$^{[7]}$引入了可微分的图像信号处理模块，能够直接处理RAW格式数据；GDIP-YOLO~$^{[8]}$通过生成对抗网络动态调整增强参数，在极端低光条件下表现出色。

\subsection{{\bf Transformer}在目标检测中的应用}

Transformer架构最初在自然语言处理领域取得突破，随后被成功引入计算机视觉任务。DETR~$^{[9]}$首次将Transformer应用于目标检测，通过自注意力机制建模全局上下文信息，实现了端到端的检测流程。然而，原始DETR模型存在训练收敛慢、计算复杂度高等问题。

为了解决这些问题，研究者们提出了多种改进方案。Deformable DETR~$^{[10]}$引入可变形注意力机制，通过稀疏采样减少计算量；RT-DETR~$^{[11]}$进一步优化了网络结构，在保持检测精度的同时显著提升了推理速度。

RF-DETR作为最新的实时检测模型，结合了DINOv2预训练骨干网络和多尺度可变形注意力机制。该模型在COCO数据集上实现了60+ mAP的检测精度和25+ FPS的推理速度，展现出优异的性能表现~$^{[2]}$。

\section{录用稿件格式的几点要求}

务求论点明确, 论证严谨, 文字通顺, 文字简练, 标点正确. 录用稿件请按照录用通知的要求对文章进行修改润色.
% 综述性文章的篇幅原则上不超过7页; 论文不超过4页; 评论性的文章可以放宽. 凡属国家自然科学基金、省部级以上重点攻关及“863”高科技项目的稿件将优先发表.

\subsection{名词、术语和单位的书写}

全篇同一名词、术语和符号须前后一致. 每个符号, 在它第一次出现时必须予以说明, 习惯用法除外. 物理量单位采用国家标准单位 (SI), 用规范化符号表示, 如压强单位应为~P (帕斯卡), 不用~``毫米汞柱''. 外文缩写词在第一次出现时注明全称. 文章统一使用英文的标点、符号, 英文的标点和数字后加一个空格, 中文的标点和数字后面不需加空格.


\subsection{定理和证明}

\begin{theorem}
这是一个定理
\end{theorem}

\begin{example}
这是一个例子
\end{example}

\begin{definition}
这是一个定义
\end{definition}

\begin{corollary}
这是一个推论
\end{corollary}

\begin{lemma}
这是一个引理
\end{lemma}


\subsection{数学公式}



公式中的变量和常量须全篇统一编码, 按照国家标准规定使用符号;
普通变量用斜体; 矢量和矩阵用粗/斜体字母;
5 个常用数集~$\mathbf{R}$、$\mathbf{C}$、$\mathbf{N}$、$\mathbf{Z}$、$\mathbf{Q}$ 用粗正体,
其它集用斜体; 上下标的位置和书写须准确 (上下文须注意全篇统一);
专用函数如~exp、sin、cos、lim、log、det 等用正体; 微分、导数、偏微分符号、数学常数用正体.
大写希腊字母~$\Gamma\,\Delta\,\Theta\,\Lambda\,\Xi\,\Pi\,\Sigma\,
\Upsilon\,\Phi\,\Psi\,\Omega$ 用正体, 为变量 (常量) 时用大写斜体. et al.,
etc., e.g., s.t. 用正体.

式~(1)和(2) 是两个简单的数学公式的例子.
\begin{equation}
Y_t^{e,k} = r_{}^e\left( {{{\boldsymbol s}_t}} \right) + \gamma \mathop {\max }\limits_{{a^k}} \left( {Q\left( {{{\boldsymbol s}_{t + 1}},{a^k};{{\boldsymbol \theta} ^k}} \right)} \right)
\end{equation}
\begin{equation}
L\left( {{{\boldsymbol \theta} ^k}} \right) = {P_s}{L^o}\left( {{{\boldsymbol \theta} ^k}} \right) + \left( {1 - {P_s}} \right){L^e}\left( {{{\boldsymbol \theta} ^k}} \right)
\end{equation}

式~(3) 是一个通栏的公式的例子. 在这个例子中使用了~\verb|\end{multicols}|和 \verb|\begin{multicols}{2}| 以及~\verb|\onecolumn| 命令.

\subsection{图表}


文中图表只附最必要的, 并附中英文的图题和表题.
图和表的分辨率用国家标准中推荐使用的线性尺寸,
图和表的绘制应遵循一个重要原则~``文字不如表/图'' 的原则.  1) 字体:
图表中注字用英文~Times New Roman 字体, 变量用斜体,
其它内容的字体为平台. 2) 请用三线表格式. 3) 存储格式: eps
\linebreak 存储选项为: 预览图, 编码~ASCII, 不选~Postscript.
请将这些图片的原始图~(.fig, .vsd, .doc 等)
放在一个单独的文件夹内压缩打包~(.zip或.rar), E-mail 给学报编辑部.
下面是图表的几种生成方式. 图~1 是一个双栏图片示例,
图~2 是一个通栏图片示例,
在~\verb|\end{multicols}|和\verb|\begin{multicols}{2}| 以及~\verb|\onecolumn| 命令.

\begin{center}
{\centering
\vbox{\centerline{\includegraphics[width=6cm]{Fig1.eps}} \vskip1mm {\small
图\ 1\quad 双栏图片示例
\\
Fig. 1\quad  An  example graph in two column }}}
\end{center}

《自动化学报》的表格采用的是三线表格式. 表~1 是一个双栏的表格示例, 表~2 是一个通栏的表格示例, 在~\verb|\end{multicols}|和\verb|\begin{multicols}{2}| 以及~\verb|\onecolumn| 命令.

\onecolumn
\begin{equation}
\begin{array}{l}
{L^e}\left( {{{\boldsymbol \theta} ^k}} \right)= {\mathrm{E}_{{\boldsymbol s}\sim\psi ,{\boldsymbol a}\sim\varphi ',\varphi '\sim{\pi ^*}\left( \psi  \right)}}\left[ {\sum\limits_{k = 1}^N {{{\left( {Y_{}^{e,k} - {Q^k}\left( {{\boldsymbol s},a_{}^k;{{\boldsymbol \theta} ^k}} \right)} \right)}^2}} } \right]
\end{array}
\end{equation}

\begin{center}
{\centering
\vbox{\centerline{\includegraphics[width=16cm]{Fig2.eps}} \vskip1mm {\small
图\ 2\quad 通栏图片示例
\\
Fig. 2\quad  An example graph in one column }}}
\end{center}

\begin{center}
\vbox{\centering{\small 表~2\quad 通栏表格示例
\\
Table 2 \quad An example table in one column } \vskip2mm
\renewcommand{\baselinestretch}{1.2}
{\footnotesize\centerline{\tabcolsep=15pt\begin{tabular*}{\textwidth}{ccccc}
\toprule
对手 (OPP)                   & 比分 (CSU : OPP) & 我队禁区控球时间 & 对手禁区控球时间 & 我队禁区控球时间比值 \\
\hline
Cyberoos2001        & \phantom{0}3:0  & 71.5 & 28.5 & 2.51 \\
FCPortugal2001      & \phantom{0}1:0  & 68.4 & 31.6 & 2.16 \\
Gemini              & 26:0 & 59.7 & 40.3 & 1.48 \\
Harmony             & \phantom{0}3:0  & 69.9 & 30.1 & 2.32 \\
Lazarus             & 11:0 & 57.3 & 42.7 & 1.34 \\
MRB                 & \phantom{0}2:0  & 63.2 & 32.8 & 1.93 \\
SBCe                & \phantom{0}4:1  & 65.8 & 34.2 & 1.92 \\
UvA\_Trilearn\_2001 & \phantom{0}1:0  & 54.9 & 44.1 & 1.24 \\
UTUtd               & 10:0 & 70.7 & 29.3 & 2.41 \\
WrightEagle2001     & \phantom{0}3:1  & 66.2 & 33.8 & 1.96 \\
Average             &      & 64.8 & 35.2 & 1.84 \\
\bottomrule
\end{tabular*}}}}\end{center}
\begin{multicols}{2}%开始双栏显示

\begin{center}
\vbox{\centering{\small 表\ 1 \quad 双栏表格示例
\\
Table 1 \quad An example table in two column} \vskip2mm
\renewcommand{\baselinestretch}{1.2}
{\footnotesize\centerline{\tabcolsep=15pt\begin{tabular}{ccccc}
\toprule
基因 & BCDQN & PMDQN\\
\hline
1 & TP53 & TP53\\
2 & FAM91A1 & PIK3CA\\
3 & TNFRSF11B &TG\\
4 & KCNQ3 & HHLA1\\
5 & MYC & ASAP1\\
6 & COL14A1 &CASC8\\
7 & CCDC26 & SNORA12\\
8 & CCN3 & MYC\\
9 & PVT1 & PVT1\\
10 & DSCC1 & RN7SL329\\
\bottomrule
\end{tabular}}}}\end{center}

\subsection{参考文献}

参考文献只列公开出版的文献. 内部资料、未发表或待发表的文献 (学位论文除外) 等请勿作为参考文献. 如必须引用时, 可放在当页的脚注 (书写格式与参考文献相同). 参考文献要按文中出现的先后次序编号~$1,2, \cdots$, 并顺序排列. 文中引用参考文献时须加注, 用~[] 表示. 参考文献中不使用任何连接词. 英文作者姓名书写时姓在前名在后, 可用~``et al.'', 但必须标注全部作者姓名. 注意参考文献的各项内容应齐全, 否则将影响文章的正常出版和被证引用, 未引用的文献不要列在参考文献列表中. 格式可参照模板末尾示例.

\TeX 的参考文献的引用比较方便, 比如要引用~Knuth 的~The \TeX book, 只需要输入~``The \TeX book\verb|$^{[1]}$|'' 得到想要的效果~``The \TeX book$^{[1]}$''. 对中文参考文献~``多模态优化算法\,\!$^{[2]}$'' 得到想要的效果~``多模态优化算法\,\!$^{[2]}$''.

\subsection{作者简介和照片}

文章末页给出所有作者的中英文简介, 篇幅不要超过~100 字, 内容包括出生年月、性别、职称、学习工作经历, 以及研究方向.


作者照片请选用简单背景的证件照片.

% 如果作者照片没有, 请用biographynophoto命令只写作者简介. 如果所有作者都没有照片, 这时没有照片文件可以引用.

\section{其他注意事项}

1) 对本刊的稿件实行三审制, 稿件录用后请勿再投其它刊物.
% 修改加工后的稿件请按录用通知要求在指定日期内返回编辑部. 如不能按期返回, 录用通知将自动失效, 稿件按自动撤稿处理.

2) 请作者仔细按照录用通知和稿件模板中的各项要求认真加工修改稿件, 若修改稿不符合要求, 编辑部有权将稿件退回作者修改直至符合要求为止,  否则造成的稿件不能及时刊出将由作者本人负责.
% 作者投稿时请确定, 文章默认的第一作者为通讯作者. 如果第一作者发生变动或通讯作者的联系方式有变动, 请及时通知编辑部.

3) 文章在编辑加工过程中, 编辑部会将~pdf 校样通过~E-mail 发给全部作者, 请各位作者对自己的稿件内容进行校对. 对编辑部发出的校样请在两天内返回. 校样中内容的修改请慎重, 需要和同事或专家对自己的文章进行一次全面的检查, 以免出现新的错误和疏漏, 影响文章的顺利出版.

4) 对修改稿加工过程中若有疑问请及时与编辑部联系, 谢谢合作!

\section{结论}

文中的结论部分不要和摘要和前言的内容重复.

\section*{致谢}

论文作者可以在此处向支持和帮助本研究和论文写作的组织或个人表示感谢.
注意, 资助本研究的基金项目已直接标注在首页页脚处, 不必在此处再次感谢.

\begin{thebibliography}{99}
\zihao{6} \addtolength{\itemsep}{0.2em} \urlstyle{rm}
\bibitem{1} Wang X S, Gu Y , Cheng Y H, Liu A P, Chen C L P. Approximate policy-based accelerated deep reinforcement learning. {\sl IEEE Transactions on Neural Networks and Learning Systems}, 2020, {\bf 31}(6): 1820$-$1830

\bibitem{2} Zhang C Q, Han Z B, Cui Y J, Hu Q. CPM-Nets: Cross partial multi-view networks. In: Proceedings of Advances in Neural Information Processing Systems (NIPS). Vancouver, BC, Canada: Curran Associates, 2019. 4077$-$4087

 \bibitem{3} Liu Jian, Gu Yang, Cheng Yu-Hu, Wang Xue-Song. Prediction of breast cancer pathogenic genes based on multi-agent reinforcement learning. {\sl Acta Automatica Sinica}, DOI: 10.16383/j.aas.c210583 \\
 (刘健, 谷洋, 程玉虎, 王雪松. 基于多智能体强化学习的乳腺癌致病基因预测. 自动化学报, DOI: 10.16383/j.aas.c210583)

 \bibitem{4} Sun Y, Wang X G, Tang X O. Deep learning face representation from predicting 10 000 classes. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR). Columbus, OH, USA: IEEE, 2014. 1891$-$1898

\bibitem{5} Jin K H, McCann M T, Froustey E, Unser M. Deep convolutional neural network for inverse problems in imaging. {\sl IEEE Transactions on Image Processing}, 2017, {\bf 26}(9): 4509$-$4522

% \bibitem{6} Pang W Y, Fan J L, Jiang Y, LewisFrank L. Optimal output regulation of partially linear discrete-time systems using reinforcement learning. {\sl Acta Automatica Sinica}, 2021, {\bf 47}(x): 1$-$12 doi: 10.16383/j.aas.c190853, to be published \\
% (庞文宇, 范家璐, 蒋瑛, LewisFrank L. 基于强化学习的部分线性离散时间系统最优输出调节. 自动化学报, 2021, {\bf 47}(x): 1$-$12 doi: 10.16383/j.aas.c190853)

% \bibitem{7} Shi W, Feng Y H, Cheng G Q, Huang H L, Huang J C, Liu Z, et al. Research on multi-aircraft cooperative air combat method based on deep reinforcement learning. {\sl Acta Automatica Sinica}, 2021, {\bf 47}(x): 1$-$14 doi: 10.16383/j.aas.c201059, to be published \\
% (施伟, 冯宇航, 程光权, 黄海龙, 黄建成, 刘准, 等. 基于深度强化学习的多机协同空战方法研究. 自动化学报, 2021, {\bf 47}(x): 1$-$14 doi: 10.16383/j.aas.c201059)

\bibitem{8} Cheng Y H, Huang L Y, Wang X S. Authentic boundary proximal policy optimization. {\sl IEEE Transactions on Cybernetics}, DOI: 10.1109/TCYB.2021.3051456

\bibitem{9} Zhou B, Lapedriza A, Khosla A, Khosla A, Oliva A, Torralba A. Places: A 10 million image database for scene recognition. {\sl IEEE Transactions on Pattern Analysis and Machine Intelligence}, 2018, {\bf 40}(6): 1452$-$1464
\end{thebibliography}

\begin{biography}[ssl.eps]
\noindent{\hei
商淑琳
}\quad
中国科学院自动化研究所博士研究生.
2002 年获北京师范大学信息学院电子系学士学位.
主要研究方向为图像和视频压缩编码.\\E-mail: aas\<EMAIL>

\noindent({\bf
SHANG Shu-Lin
}\quad
Ph.D. candidate at the
Institute of Automation, Chinese Academy of Sciences. He received
his bachelor degree from Beijing Normal University in 2002. His research
interest covers image compression and video coding.)
\end{biography}

\begin{biography}[nmzuo.eps]
\noindent{\hei
左念明
}\quad
中国科学院自动化研究所博士研究生.
2002 年获山东大学数学学院学士学位.
主要研究方向为医学图像, CT 图像重建. 本文通信作者.
\\E-mail: aas\<EMAIL>

\noindent({\bf
ZUO Nian-Ming
}\quad
Ph.D. candidate at the Institute of Automation, Chinese Academy of Sciences. He received his bachelor degree from Shandong University in 2002. His research interest covers medical CT image reconstruction and medical image processing. Corresponding author of this paper.)
\\
\\
\\
\\
\\
\\
\\
\\
\\
\\
\\
\\
\end{biography}

\end{multicols}
\end{document}
