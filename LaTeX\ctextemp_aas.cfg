\def\CTeXPreproc{Created by ctex v0.2.12, don't edit!}%%
%%
%%  自动化学报
%%
%% Copyright (C) 2006 by SHAN<PERSON>lin<<EMAIL>> ZUO Nianming
%% 
%% This file may be distributed and/or modified under the
%% conditions of the LaTeX Project Public License, either version 1.3a
%% of this license or (at your option) any later version.
%% The latest version of this license is in:
%% 
%% http://www.latex-project.org/lppl.txt
%% 
%% and version 1.3a or later is part of all distributions of LaTeX
%% version 2004/10/01 or later.
%%
\makeatletter
%%------------------aas@firstheadings begin------------------------------------%%
\def\ps@aas@firstheadings{
   \let\@evenhead\@empty\let\@evenfoot\@empty
   \let\@oddhead\@empty\let\@oddfoot\@empty
   \def\@evenhead{%
       \vbox{%
           \hbox to \textwidth{\small 第 XX 卷 第 X 期 \hfill 自\kern0.5em 动\kern0.5em 化\kern0.5em 学\kern0.5em 报 \hfill Vol.\ XX, No.\ X}%
           \vskip 1.5mm%
           \hbox to \textwidth{\small 202X 年 X 月 \hfill ACTA AUTOMATICA SINICA \hfill Month, 202X}%
           \vskip 1.0mm%
           \hrule width\textwidth height0.63pt\vskip0.85pt \hrule width\textwidth height0.63pt%
       }
   }
   \let\@oddhead\@evenhead%
   \let\@oddfoot\@evenfoot%
}
%%-----------------aas@firstheadings end--------------------------------------%%

%%-------------------aasheadings begin----------------------------------------%%
\def\ps@aasheadings{
    \let\@evenhead\@empty\let\@evenfoot\@empty
    \let\@oddhead\@empty\let\@oddfoot\@empty
    \def\@evenhead{%
        \vbox{%
            \vskip 2.2mm%
            \hbox to \textwidth {\small\thepage \hfill 自\kern2.5em 动\kern2.5em 化\kern2.5em 学\kern2.5em 报 \hfill 第 XX 卷}%
            \vskip 1.5mm%
            \hbox to \textwidth{\noindent\rule[2mm]{\textwidth}{0.5pt}}%
        }%
    }
    \def\@oddhead{%
        \vbox{%
            \vskip 2.2mm%
            \hbox to \textwidth {\small X期 \hfill 商淑琳 等: 自动化学报论文撰写指南 \hfill \small\thepage}%
            \vskip 1.5mm%
            \hbox to \textwidth{\noindent\rule[2mm]{\textwidth}{0.5pt}}%  
        }
    }%
}
%---------------------headings end------------------------------------------%%
\makeatother
