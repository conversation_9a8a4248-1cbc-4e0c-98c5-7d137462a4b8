#!/usr/bin/env python3
"""
Convert PNG images to PDF format for LaTeX compilation
"""

import os
from PIL import Image
import sys

def convert_png_to_pdf(png_path, pdf_path):
    """Convert a PNG image to PDF format"""
    try:
        # Open the PNG image
        with Image.open(png_path) as img:
            # Convert to RGB if necessary (PDF doesn't support transparency)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create a white background
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Save as PDF
            img.save(pdf_path, 'PDF', resolution=300.0, quality=95)
            print(f"Successfully converted {png_path} to {pdf_path}")
            return True
    except Exception as e:
        print(f"Error converting {png_path}: {e}")
        return False

def main():
    # List of PNG files to convert
    png_files = [
        "检测效果图.png",
        "热力图.png", 
        "训练指标图.png"
    ]
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    success_count = 0
    for png_file in png_files:
        png_path = os.path.join(current_dir, png_file)
        pdf_file = png_file.replace('.png', '.pdf')
        pdf_path = os.path.join(current_dir, pdf_file)
        
        if os.path.exists(png_path):
            if convert_png_to_pdf(png_path, pdf_path):
                success_count += 1
        else:
            print(f"PNG file not found: {png_path}")
    
    print(f"\nConversion completed: {success_count}/{len(png_files)} files converted successfully")

if __name__ == "__main__":
    main()
